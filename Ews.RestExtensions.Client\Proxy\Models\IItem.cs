﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    public partial class IItem
    {
        /// <summary>
        /// Initializes a new instance of the IItem class.
        /// </summary>
        public IItem() { }

        /// <summary>
        /// Initializes a new instance of the IItem class.
        /// </summary>
        public IItem(string id = default(string), string parentId = default(string), string modelType = default(string))
        {
            Id = id;
            ParentId = parentId;
            ModelType = modelType;
        }

        /// <summary>
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

        /// <summary>
        /// Id of the Container which the item is found in.
        /// </summary>
        [JsonProperty(PropertyName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// The ModelType of the item. Possible values include: 'Container',
        /// 'Value', 'Trend', 'Alarm', 'Enum', 'AlarmEventType'
        /// </summary>
        [JsonProperty(PropertyName = "ModelType")]
        public string ModelType { get; private set; }

    }
}
