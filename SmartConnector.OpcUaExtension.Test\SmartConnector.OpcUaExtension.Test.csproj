<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{A1B2C3D4-5E6F-7A8B-9C0D-1E2F3A4B5C6D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartConnector.OpcUaExtension.Test</RootNamespace>
    <AssemblyName>SmartConnector.OpcUaExtension.Test</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Client, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Ews.Client.2.4.10\lib\net452\Ews.Client.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Common, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Ews.Common.2.4.10\lib\net452\Ews.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Server.Contract, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Ews.Server.Contract.2.4.10\lib\net452\Ews.Server.Contract.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Common, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Common.2.4.10\lib\net452\Mongoose.Common.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Ews.Server.Data, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Ews.Server.Data.2.4.10\lib\net452\Mongoose.Ews.Server.Data.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Process, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Process.2.4.10\lib\net452\Mongoose.Process.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.4.12\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="nunit.framework, Version=3.12.0.0, Culture=neutral, PublicKeyToken=2638cd05610744eb, processorArchitecture=MSIL">
      <HintPath>..\packages\NUnit.3.12.0\lib\net45\nunit.framework.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Client, Version=1.4.371.86, Culture=neutral, PublicKeyToken=96c1b444d7e4b8b9, processorArchitecture=MSIL">
      <HintPath>..\packages\OPCFoundation.NetStandard.Opc.Ua.Client.1.4.371.86\lib\net462\Opc.Ua.Client.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Core, Version=1.4.371.86, Culture=neutral, PublicKeyToken=96c1b444d7e4b8b9, processorArchitecture=MSIL">
      <HintPath>..\packages\OPCFoundation.NetStandard.Opc.Ua.1.4.371.86\lib\net462\Opc.Ua.Core.dll</HintPath>
    </Reference>
    <Reference Include="SxL.Common, Version=2.4.10.0, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\SxL.Common.2.4.10\lib\net452\SxL.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="OpcUaHelperFixture.cs" />
    <Compile Include="SetupProcessorFixture.cs" />
    <Compile Include="UpdateProcessorFixture.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\SmartConnector.OpcUaExtension\SmartConnector.OpcUaExtension.csproj">
      <Project>{F8E2A1B3-4C5D-4E6F-8A9B-1C2D3E4F5A6B}</Project>
      <Name>SmartConnector.OpcUaExtension</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
