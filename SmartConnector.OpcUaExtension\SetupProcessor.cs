using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Ews.Common;
using Mongoose.Common;
using Mongoose.Common.Attributes;
using Mongoose.Ews.Server.Data;
using Mongoose.Ews.Server.Data.Shared;

namespace SmartConnector.OpcUaExtension
{
    [ConfigurationDefaults("OPC UA Setup Processor", "Ensures the presence of an EWS Server and OPC UA client configuration, bootstrapping whatever needs to be there.")]
    public class SetupProcessor : OpcUaProcessorBase
    {
        #region EwsAddress
        /// <summary>
        /// The address 
        /// </summary>
        [Required, DefaultValue("http://localhost:5400/SmartConnectorOpcUaService"), Tooltip("Default HTTP address to configure when bootstraping the EWS Server")]
        public string EwsAddress { get; set; }
        #endregion

        #region Realm
        [DefaultValue("OpcUaRealm"), Toolt<PERSON>("Realm value for HTTP Digest Authentication")]
        public string Realm { get; set; }
        #endregion

        #region NumberOfNodeMappings
        [DefaultValue(10), <PERSON><PERSON><PERSON>("Number of OPC UA node mappings to create")]
        public int NumberOfNodeMappings { get; set; } = 10;
        #endregion

        #region Execute_Subclass - Override
        protected override IEnumerable<Prompt> Execute_Subclass()
        {
            // Make sure we can connect to an EWS Server
            if (!IsEwsConnected) return new List<Prompt> { CreateCannotConnectPrompt() };

            // If the server isn't running, start it now.
            if (!DataAdapter.Server.IsRunning) DataAdapter.StartServer();

            // Confirm that the server settings are how we want them be.
            EnsureServerParameters();

            // Add the OPC UA connection configuration fields
            AddOpcUaConfigurationFields();

            // Add the node mapping configuration
            AddNodeMappingConfiguration();

            // Return any issues
            return new List<Prompt>();
        }
        #endregion

        #region CreateEwsServer - Override
        protected override EwsServerDataAdapter CreateEwsServer()
        {
            return EwsServerDataAdapter.ConnectNew(ServerName, EwsAddress, Realm, UserName, Password, 
                true, true, "SmartConnector.OpcUaExtension.dll", "SmartConnector.OpcUaExtension.EwsServer.CustomEwsServiceHost");
        }
        #endregion

        #region EnsureServerParameters
        private void EnsureServerParameters()
        {
            CheckCancellationToken();

            DataAdapter.ModifyServerIsAutoStart(true);
            DataAdapter.ModifyServerAllowCookies(true);
            DataAdapter.ModifyServerPageSize(1000);
            DataAdapter.ModifyServerRootContainerItemAlternateId("RootContainer");
            DataAdapter.ModifyServerRootContainerItemDescription("All folders derive from here");
            EnsureSupportedMethods();
        }
        #endregion

        #region EnsureSupportedMethods
        /// <summary>
        /// Disable EWS Server functions that aren't needed in this solution and enable those that are.
        /// </summary>
        private void EnsureSupportedMethods()
        {
            CheckCancellationToken();

            DataAdapter.ModifyServerSupportedMethods(new EwsServerMethods
            {
                AcknowledgeAlarmEvents = false,
                ForceValues = false,
                GetAlarmEventTypes = false,
                GetAlarmEvents = false,
                GetAlarmHistory = false,
                GetEnums = false,
                GetHierarchicalInformation = false,
                GetUpdatedAlarmEvents = false,
                UnforceValues = false,

                GetContainerItems = true,
                GetHistory = true,
                GetItems = true,
                GetNotification = true,
                GetValues = true,
                GetWebServiceInformation = true,
                Renew = true,
                SetValues = true,
                Subscribe = true,
                Unsubscribe = true
            });
        }
        #endregion

        #region EnsureContainerItem
        private EwsContainerItem EnsureContainerItem(string altId, string name = null, string description = null, EwsContainerTypeEnum type = EwsContainerTypeEnum.Folder, EwsContainerItem parent = null)
        {
            CheckCancellationToken();

            var ci = DataAdapter.ContainerItems.FirstOrDefault(x => x.AlternateId == altId);
            if (ci == null) return DataAdapter.AddContainerItem(altId, name ?? altId, description, type, parent);
            ci = DataAdapter.ModifyContainerItemName(ci, name ?? altId);
            ci = DataAdapter.ModifyContainerItemDescription(ci, description);
            ci = DataAdapter.ModifyContainerItemType(ci, type);
            return DataAdapter.ModifyContainerItemParent(ci, parent);
        }
        #endregion

        #region EnsureValueItem
        private EwsValueItem EnsureValueItem(string altId, string name = null, string description = null, EwsValueTypeEnum type = EwsValueTypeEnum.String, EwsContainerItem parent = null, string unit = null, EwsValueWriteableEnum writeable = EwsValueWriteableEnum.Writeable, EwsValueForceableEnum forceable = EwsValueForceableEnum.Forceable, EwsValueStateEnum defaultState = EwsValueStateEnum.Uncertain)
        {
            CheckCancellationToken();

            var vi = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == altId);
            if (vi == null) return DataAdapter.AddValueItem(altId, name ?? altId, description, type, writeable, forceable, defaultState, unit, parent);
            vi = DataAdapter.ModifyValueItemName(vi, name ?? altId);
            vi = DataAdapter.ModifyValueItemDescription(vi, description);
            vi = DataAdapter.ModifyValueItemType(vi, type);
            vi = DataAdapter.ModifyValueItemWriteable(vi, writeable);
            vi = DataAdapter.ModifyValueItemForceable(vi, forceable);
            vi = DataAdapter.ModifyValueItemUnit(vi, unit);
            return DataAdapter.ModifyValueItemParent(vi, parent);
        }
        #endregion

        #region EnsureHistoryItem
        private EwsHistoryItem EnsureHistoryItem(string altId, string name, string description, EwsValueItem valueItem, EwsContainerItem parent = null)
        {
            CheckCancellationToken();

            var hi = DataAdapter.HistoryItems.FirstOrDefault(x => x.AlternateId == altId);
            if (hi == null) return DataAdapter.AddHistoryItem(altId, name ?? altId, description, valueItem, true, parent);
            hi = DataAdapter.ModifyHistoryItemName(hi, name ?? altId);
            hi = DataAdapter.ModifyHistoryItemDescription(hi, description);
            return DataAdapter.ModifyHistoryItemParent(hi, parent);
        }
        #endregion

        #region AddOpcUaConfigurationFields
        /// <summary>
        /// Add the configuration fields for OPC UA connection
        /// </summary>
        private void AddOpcUaConfigurationFields()
        {
            var configFolder = EnsureContainerItem("OpcUaConfiguration", "OPC UA Configuration", "Configuration settings for OPC UA connection");

            // OPC UA Endpoint URL
            var endpointItem = EnsureValueItem(OpcUaHelper.OpcUaEndpointId, "OPC UA Endpoint", "OPC UA server endpoint URL", EwsValueTypeEnum.String, configFolder, null, EwsValueWriteableEnum.Writeable, EwsValueForceableEnum.NotForceable);
            if (string.IsNullOrEmpty(endpointItem.Value)) 
                endpointItem = DataAdapter.ModifyValueItemValue(endpointItem, OpcUaEndpointUrl ?? "opc.tcp://localhost:4840");

            // Connection Status
            EnsureValueItem(OpcUaHelper.OpcUaConnectionStatusId, "Connection Status", "Current OPC UA connection status", EwsValueTypeEnum.String, configFolder, null, EwsValueWriteableEnum.ReadOnly, EwsValueForceableEnum.NotForceable);

            // Last Connected Time
            EnsureValueItem(OpcUaHelper.OpcUaLastConnectedId, "Last Connected", "Last successful OPC UA connection time", EwsValueTypeEnum.DateTime, configFolder, null, EwsValueWriteableEnum.ReadOnly, EwsValueForceableEnum.NotForceable);

            // Error Message
            EnsureValueItem(OpcUaHelper.OpcUaErrorMessageId, "Error Message", "Last OPC UA connection error message", EwsValueTypeEnum.String, configFolder, null, EwsValueWriteableEnum.ReadOnly, EwsValueForceableEnum.NotForceable);
        }
        #endregion

        #region AddNodeMappingConfiguration
        /// <summary>
        /// Add the node mapping configuration containers and fields
        /// </summary>
        private void AddNodeMappingConfiguration()
        {
            var mappingsFolder = EnsureContainerItem(OpcUaHelper.DefaultNodeMappingsContainerId, "Node Mappings", "OPC UA node to EWS value mappings");

            for (var i = 1; i <= NumberOfNodeMappings; i++)
            {
                AddNodeMapping(mappingsFolder, i);
            }
        }

        private void AddNodeMapping(EwsContainerItem parentContainer, int mappingNumber)
        {
            var containerId = OpcUaHelper.GetNodeMappingContainerId(mappingNumber);
            var parent = EnsureContainerItem(containerId, $"Mapping {mappingNumber}", $"OPC UA node mapping #{mappingNumber}", EwsContainerTypeEnum.Folder, parentContainer);

            // Node ID to read from OPC UA server
            var nodeIdItem = EnsureValueItem(OpcUaHelper.GetNodeMappingNodeIdId(mappingNumber), "Node ID", "OPC UA Node ID to read", EwsValueTypeEnum.String, parent, null, EwsValueWriteableEnum.Writeable, EwsValueForceableEnum.NotForceable);
            if (string.IsNullOrEmpty(nodeIdItem.Value))
            {
                // Set a default example node ID
                nodeIdItem = DataAdapter.ModifyValueItemValue(nodeIdItem, $"ns=2;s=Demo.Dynamic.Scalar.Int32", EwsValueStateEnum.Uncertain);
            }

            // Current value from OPC UA
            var valueItem = EnsureValueItem(OpcUaHelper.GetNodeMappingValueId(mappingNumber), "Value", "Current value from OPC UA node", EwsValueTypeEnum.String, parent, null, EwsValueWriteableEnum.ReadOnly, EwsValueForceableEnum.NotForceable);
            EnsureHistoryItem(OpcUaHelper.GetNodeMappingHistoryId(mappingNumber), "Value History", "Historical values from OPC UA node", valueItem, parent);

            // Quality/Status
            EnsureValueItem(OpcUaHelper.GetNodeMappingQualityId(mappingNumber), "Quality", "OPC UA value quality/status", EwsValueTypeEnum.String, parent, null, EwsValueWriteableEnum.ReadOnly, EwsValueForceableEnum.NotForceable);

            // Timestamp
            EnsureValueItem(OpcUaHelper.GetNodeMappingTimestampId(mappingNumber), "Timestamp", "OPC UA value timestamp", EwsValueTypeEnum.DateTime, parent, null, EwsValueWriteableEnum.ReadOnly, EwsValueForceableEnum.NotForceable);
        }
        #endregion
    }
}
