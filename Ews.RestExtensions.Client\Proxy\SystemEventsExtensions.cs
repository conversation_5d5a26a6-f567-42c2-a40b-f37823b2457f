﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for SystemEvents.
    /// </summary>
    public static partial class SystemEventsExtensions
    {
            /// <summary>
            /// Returns all SystemEvent which fit the filter criteria supplied. All filter
            /// parameters are optional. SystemEvent will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='priorityFrom'>
            /// Return only SystemEvent with a Priority greater or equal to the value
            /// supplied.
            /// </param>
            /// <param name='priorityTo'>
            /// Return only SystemEvent with a Priority less or equal to the value
            /// supplied.
            /// </param>
            /// <param name='type'>
            /// Return only SystemEvent with this Type value.
            /// </param>
            /// <param name='occurredOnOrAfter'>
            /// Return only SystemEvent with OccurredOn which is on or after this value.
            /// </param>
            /// <param name='occurredBefore'>
            /// Return only SystemEvent with OccurredOn which is before this value.
            /// </param>
            /// <param name='orderBy'>
            /// SystemEvent will be returned in this order.  OccurredOnAscending by
            /// default. Possible values include: 'OccurredOnAscending',
            /// 'OccurredOnDescending'
            /// </param>
            /// <param name='take'>
            /// Number of SystemEvent that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of SystemEvent that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            public static IList<SystemEventModel> Retrieve(this ISystemEvents operations, int? priorityFrom = default(int?), int? priorityTo = default(int?), string type = default(string), DateTime? occurredOnOrAfter = default(DateTime?), DateTime? occurredBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveAsync(priorityFrom, priorityTo, type, occurredOnOrAfter, occurredBefore, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all SystemEvent which fit the filter criteria supplied. All filter
            /// parameters are optional. SystemEvent will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='priorityFrom'>
            /// Return only SystemEvent with a Priority greater or equal to the value
            /// supplied.
            /// </param>
            /// <param name='priorityTo'>
            /// Return only SystemEvent with a Priority less or equal to the value
            /// supplied.
            /// </param>
            /// <param name='type'>
            /// Return only SystemEvent with this Type value.
            /// </param>
            /// <param name='occurredOnOrAfter'>
            /// Return only SystemEvent with OccurredOn which is on or after this value.
            /// </param>
            /// <param name='occurredBefore'>
            /// Return only SystemEvent with OccurredOn which is before this value.
            /// </param>
            /// <param name='orderBy'>
            /// SystemEvent will be returned in this order.  OccurredOnAscending by
            /// default. Possible values include: 'OccurredOnAscending',
            /// 'OccurredOnDescending'
            /// </param>
            /// <param name='take'>
            /// Number of SystemEvent that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of SystemEvent that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<SystemEventModel>> RetrieveAsync(this ISystemEvents operations, int? priorityFrom = default(int?), int? priorityTo = default(int?), string type = default(string), DateTime? occurredOnOrAfter = default(DateTime?), DateTime? occurredBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(priorityFrom, priorityTo, type, occurredOnOrAfter, occurredBefore, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent requested.  Must be double URL encoded.
            /// </param>
            public static SystemEventModel RetrieveById(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<SystemEventModel> RetrieveByIdAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Message property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose Message property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveMessage(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveMessageAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Message property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose Message property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveMessageAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveMessageWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the OccurredOn property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose OccurredOn property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            public static DateTime? RetrieveOccurredOn(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveOccurredOnAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the OccurredOn property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose OccurredOn property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<DateTime?> RetrieveOccurredOnAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveOccurredOnWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Priority property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose Priority property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static int? RetrievePriority(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrievePriorityAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Priority property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose Priority property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<int?> RetrievePriorityAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrievePriorityWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the SourceId property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose SourceId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveSourceId(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveSourceIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the SourceId property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose SourceId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveSourceIdAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveSourceIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the SourceName property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose SourceName property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            public static string RetrieveSourceName(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveSourceNameAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the SourceName property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose SourceName property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveSourceNameAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveSourceNameWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Type property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose Type property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveType(this ISystemEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISystemEvents)s).RetrieveTypeAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Type property for the SystemEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the SystemEvent whose Type property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveTypeAsync(this ISystemEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTypeWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
