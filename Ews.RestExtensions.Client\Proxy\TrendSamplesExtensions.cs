﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for TrendSamples.
    /// </summary>
    public static partial class TrendSamplesExtensions
    {
            /// <summary>
            /// Returns all TrendSample which fit the filter criteria supplied. All filter
            /// parameters are optional except trendId. TrendSample will be ordered and
            /// paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='trendId'>
            /// The Id of the Trend which TrendSample are to be returned for.  This
            /// parameter is not optional.
            /// </param>
            /// <param name='state'>
            /// Return only TrendSample with this State value. Possible values include:
            /// 'Good', 'Uncertain', 'Forced', 'Offline', 'Error'
            /// </param>
            /// <param name='sampledOnOrAfter'>
            /// Return only TrendSample with SampleDate which is on or after this value.
            /// </param>
            /// <param name='sampledBefore'>
            /// Return only TrendSample with SampleDate which is before this value.
            /// </param>
            /// <param name='orderBy'>
            /// TrendSample will be returned in this order.  SampleDateAscending by
            /// default. Possible values include: 'SampleDateAscending',
            /// 'SampleDateDescending'
            /// </param>
            /// <param name='take'>
            /// Number of TrendSample that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of TrendSample that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            public static IList<TrendSampleModel> Retrieve(this ITrendSamples operations, string trendId, string state = default(string), DateTime? sampledOnOrAfter = default(DateTime?), DateTime? sampledBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveAsync(trendId, state, sampledOnOrAfter, sampledBefore, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all TrendSample which fit the filter criteria supplied. All filter
            /// parameters are optional except trendId. TrendSample will be ordered and
            /// paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='trendId'>
            /// The Id of the Trend which TrendSample are to be returned for.  This
            /// parameter is not optional.
            /// </param>
            /// <param name='state'>
            /// Return only TrendSample with this State value. Possible values include:
            /// 'Good', 'Uncertain', 'Forced', 'Offline', 'Error'
            /// </param>
            /// <param name='sampledOnOrAfter'>
            /// Return only TrendSample with SampleDate which is on or after this value.
            /// </param>
            /// <param name='sampledBefore'>
            /// Return only TrendSample with SampleDate which is before this value.
            /// </param>
            /// <param name='orderBy'>
            /// TrendSample will be returned in this order.  SampleDateAscending by
            /// default. Possible values include: 'SampleDateAscending',
            /// 'SampleDateDescending'
            /// </param>
            /// <param name='take'>
            /// Number of TrendSample that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of TrendSample that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<TrendSampleModel>> RetrieveAsync(this ITrendSamples operations, string trendId, string state = default(string), DateTime? sampledOnOrAfter = default(DateTime?), DateTime? sampledBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(trendId, state, sampledOnOrAfter, sampledBefore, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the TrendSample with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample requested.  Must be double URL encoded.
            /// </param>
            public static TrendSampleModel RetrieveById(this ITrendSamples operations, string id)
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the TrendSample with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<TrendSampleModel> RetrieveByIdAsync(this ITrendSamples operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the State property for the TrendSample with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose State property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveState(this ITrendSamples operations, string id)
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveStateAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the State property for the TrendSample with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose State property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveStateAsync(this ITrendSamples operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveStateWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the SampleDate property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose SampleDate property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            public static DateTime? RetrieveSampleDate(this ITrendSamples operations, string id)
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveSampleDateAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the SampleDate property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose SampleDate property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<DateTime?> RetrieveSampleDateAsync(this ITrendSamples operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveSampleDateWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the TrendModel for the TrendSample with the supplied Id.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose TrendModel is to be returned.  Requires double
            /// URL encoding.
            /// </param>
            public static TrendModel RetrieveTrend(this ITrendSamples operations, string id)
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveTrendAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the TrendModel for the TrendSample with the supplied Id.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose TrendModel is to be returned.  Requires double
            /// URL encoding.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<TrendModel> RetrieveTrendAsync(this ITrendSamples operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTrendWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the TrendId property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose TrendId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static object RetrieveTrendId(this ITrendSamples operations, string id)
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveTrendIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the TrendId property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose TrendId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> RetrieveTrendIdAsync(this ITrendSamples operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTrendIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Value for the TrendSample with the supplied Id.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose Value is to be returned.  Requires double URL
            /// encoding.
            /// </param>
            public static string RetrieveValue(this ITrendSamples operations, string id)
            {
                return Task.Factory.StartNew(s => ((ITrendSamples)s).RetrieveValueAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Value for the TrendSample with the supplied Id.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the TrendSample whose Value is to be returned.  Requires double URL
            /// encoding.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveValueAsync(this ITrendSamples operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveValueWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
