﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Containers operations.
    /// </summary>
    public partial interface IContainers
    {
        /// <summary>
        /// Creates a new Container and returns it.
        /// </summary>
        /// <param name='newItem'>
        /// Definition of how the Container will be created.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> CreateWithHttpMessagesAsync(NewContainerModel newItem, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns all Container which fit the filter criteria supplied. All
        /// filter parameters are optional. Container will be ordered and
        /// paged as requested.
        /// </summary>
        /// <param name='name'>
        /// Return only Container whose name contains this value.
        /// </param>
        /// <param name='type'>
        /// Filter by Type. Possible values include: 'Folder', 'Server',
        /// 'Device', 'Structure', 'Service'
        /// </param>
        /// <param name='orderBy'>
        /// Container will be returned in this order.  NameAscending by
        /// default. Possible values include: 'NameAscending',
        /// 'NameDescending'
        /// </param>
        /// <param name='take'>
        /// Number of Container that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of Container that should be skipped before items are
        /// returned.  0 by default.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<ContainerModel>>> RetrieveWithHttpMessagesAsync(string name = default(string), string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Container with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns all children (Value, Alarm, Trend or Container) parented
        /// by the Container with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose children are to be returned.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='type'>
        /// Return only those children which are of this type.  All returned
        /// by default. Possible values include: 'Container', 'Value',
        /// 'Trend', 'Alarm', 'Enum', 'AlarmEventType'
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<IItem>>> RetrieveChildrenWithHttpMessagesAsync(string id, string type = default(string), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Description property for the Container with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Description property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveDescriptionWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Description property for the Container with the Id
        /// provided to the new value supplied.  Returns the modified
        /// Container.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Description property is to be modified.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Description value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> UpdateDescriptionWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Name property for the Container with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Name property is to be returned.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveNameWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Name property for the Container with the Id provided
        /// to the new value supplied.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Name property is to be modified.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Name value
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> UpdateNameWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the parent Container for the Container with the Id
        /// supplied.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose parent Container is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> RetrieveParentWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the ParentId property for the Container with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose ParentId property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveParentIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the ParentId property for the Container with the Id
        /// provided to the new value supplied.  Returns the modified
        /// Container.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose ParentId property is to be modified.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New ParentId value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> UpdateParentIdWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Type property for the Container with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Type property is to be returned.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveTypeWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Type property for the Container with the Id provided
        /// to the new value supplied.  Returns the modified Container.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Type property is to be modified.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Type value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> UpdateTypeWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Container with the Id provided to the values
        /// supplied.  Returns the modified Container.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container to be modified.  Must be double URL encoded.
        /// </param>
        /// <param name='updatedItem'>
        /// Item to be updated.  All modfied properties will be changed.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> UpdateWithHttpMessagesAsync(string id, UpdateContainerModel updatedItem, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Id property for the Container with the Id provided to
        /// the new value supplied.  Returns the modified Container.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container whose Id property is to be modified.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Id value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> UpdateIdWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Deletes the Container with the Id provided.  All children will
        /// also be deleted.
        /// </summary>
        /// <param name='id'>
        /// Id of the Container to be deleted.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> DeleteWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
