﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for Containers.
    /// </summary>
    public static partial class ContainersExtensions
    {
            /// <summary>
            /// Creates a new Container and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Container will be created.
            /// </param>
            public static ContainerModel Create(this IContainers operations, NewContainerModel newItem)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).CreateAsync(newItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Creates a new Container and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Container will be created.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> CreateAsync(this IContainers operations, NewContainerModel newItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(newItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all Container which fit the filter criteria supplied. All filter
            /// parameters are optional. Container will be ordered and paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only Container whose name contains this value.
            /// </param>
            /// <param name='type'>
            /// Filter by Type. Possible values include: 'Folder', 'Server', 'Device',
            /// 'Structure', 'Service'
            /// </param>
            /// <param name='orderBy'>
            /// Container will be returned in this order.  NameAscending by default.
            /// Possible values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Container that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Container that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            public static IList<ContainerModel> Retrieve(this IContainers operations, string name = default(string), string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveAsync(name, type, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all Container which fit the filter criteria supplied. All filter
            /// parameters are optional. Container will be ordered and paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only Container whose name contains this value.
            /// </param>
            /// <param name='type'>
            /// Filter by Type. Possible values include: 'Folder', 'Server', 'Device',
            /// 'Structure', 'Service'
            /// </param>
            /// <param name='orderBy'>
            /// Container will be returned in this order.  NameAscending by default.
            /// Possible values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Container that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Container that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<ContainerModel>> RetrieveAsync(this IContainers operations, string name = default(string), string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(name, type, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container requested.  Must be double URL encoded.
            /// </param>
            public static ContainerModel RetrieveById(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> RetrieveByIdAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all children (Value, Alarm, Trend or Container) parented by the
            /// Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose children are to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='type'>
            /// Return only those children which are of this type.  All returned by
            /// default. Possible values include: 'Container', 'Value', 'Trend', 'Alarm',
            /// 'Enum', 'AlarmEventType'
            /// </param>
            public static IList<IItem> RetrieveChildren(this IContainers operations, string id, string type = default(string))
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveChildrenAsync(id, type), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all children (Value, Alarm, Trend or Container) parented by the
            /// Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose children are to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='type'>
            /// Return only those children which are of this type.  All returned by
            /// default. Possible values include: 'Container', 'Value', 'Trend', 'Alarm',
            /// 'Enum', 'AlarmEventType'
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<IItem>> RetrieveChildrenAsync(this IContainers operations, string id, string type = default(string), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveChildrenWithHttpMessagesAsync(id, type, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Description property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Description property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveDescription(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveDescriptionAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Description property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Description property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveDescriptionAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveDescriptionWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Description property for the Container with the Id provided
            /// to the new value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Description property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            public static ContainerModel UpdateDescription(this IContainers operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).UpdateDescriptionAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Description property for the Container with the Id provided
            /// to the new value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Description property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> UpdateDescriptionAsync(this IContainers operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateDescriptionWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Name property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Name property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveName(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveNameAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Name property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Name property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveNameAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveNameWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Name property for the Container with the Id provided to the
            /// new value supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Name property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value
            /// </param>
            public static ContainerModel UpdateName(this IContainers operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).UpdateNameAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Name property for the Container with the Id provided to the
            /// new value supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Name property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> UpdateNameAsync(this IContainers operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateNameWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the parent Container for the Container with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose parent Container is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static ContainerModel RetrieveParent(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveParentAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the parent Container for the Container with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose parent Container is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> RetrieveParentAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveParentWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the ParentId property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose ParentId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveParentId(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveParentIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the ParentId property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose ParentId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveParentIdAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveParentIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the ParentId property for the Container with the Id provided to
            /// the new value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose ParentId property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New ParentId value.
            /// </param>
            public static ContainerModel UpdateParentId(this IContainers operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).UpdateParentIdAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the ParentId property for the Container with the Id provided to
            /// the new value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose ParentId property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New ParentId value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> UpdateParentIdAsync(this IContainers operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateParentIdWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Type property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Type property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveType(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).RetrieveTypeAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Type property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Type property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveTypeAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTypeWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Type property for the Container with the Id provided to the
            /// new value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Type property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Type value.
            /// </param>
            public static ContainerModel UpdateType(this IContainers operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).UpdateTypeAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Type property for the Container with the Id provided to the
            /// new value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Type property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Type value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> UpdateTypeAsync(this IContainers operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateTypeWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Container with the Id provided to the values supplied.
            /// Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='updatedItem'>
            /// Item to be updated.  All modfied properties will be changed.
            /// </param>
            public static ContainerModel Update(this IContainers operations, string id, UpdateContainerModel updatedItem)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).UpdateAsync(id, updatedItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Container with the Id provided to the values supplied.
            /// Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='updatedItem'>
            /// Item to be updated.  All modfied properties will be changed.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> UpdateAsync(this IContainers operations, string id, UpdateContainerModel updatedItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateWithHttpMessagesAsync(id, updatedItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Id property for the Container with the Id provided to the new
            /// value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Id property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Id value.
            /// </param>
            public static ContainerModel UpdateId(this IContainers operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).UpdateIdAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Id property for the Container with the Id provided to the new
            /// value supplied.  Returns the modified Container.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container whose Id property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Id value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> UpdateIdAsync(this IContainers operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateIdWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes the Container with the Id provided.  All children will also be
            /// deleted.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container to be deleted.  Must be double URL encoded.
            /// </param>
            public static object Delete(this IContainers operations, string id)
            {
                return Task.Factory.StartNew(s => ((IContainers)s).DeleteAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes the Container with the Id provided.  All children will also be
            /// deleted.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Container to be deleted.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteAsync(this IContainers operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
