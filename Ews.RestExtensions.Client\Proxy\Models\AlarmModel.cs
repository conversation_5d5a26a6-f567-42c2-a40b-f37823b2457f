﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// The existence of an Alarm does not imply that an alarm has occurred,
    /// only that alarm criteria exists.
    /// Typically, this is done for a specific Value but this is not required.
    /// If alarm is associated with a Value, the Id of that Value should be
    /// included.
    /// </summary>
    public partial class AlarmModel
    {
        /// <summary>
        /// Initializes a new instance of the AlarmModel class.
        /// </summary>
        public AlarmModel() { }

        /// <summary>
        /// Initializes a new instance of the AlarmModel class.
        /// </summary>
        public AlarmModel(string state = default(string), string valueId = default(string), string transitionModel = default(string), string name = default(string), string description = default(string), string parentId = default(string), string id = default(string))
        {
            State = state;
            ValueId = valueId;
            TransitionModel = transitionModel;
            Name = name;
            Description = description;
            ParentId = parentId;
            Id = id;
        }

        /// <summary>
        /// Current state of the Alarm. Possible values include: 'Normal',
        /// 'Active', 'Acknowledged', 'Reset', 'Disabled'
        /// </summary>
        [JsonProperty(PropertyName = "State")]
        public string State { get; set; }

        /// <summary>
        /// Optional Id of a Value this Alarm is associated with.
        /// </summary>
        [JsonProperty(PropertyName = "ValueId")]
        public string ValueId { get; set; }

        /// <summary>
        /// The transition model for the Alarm. Possible values include:
        /// 'Unrestricted', 'SimpleSystemAlarm', 'NoAcknowledgeRequired',
        /// 'SimpleTransientAlarm', 'SingleAcknowledgeRequirement',
        /// 'ExtendedAcknowledgeRequirement'
        /// </summary>
        [JsonProperty(PropertyName = "TransitionModel")]
        public string TransitionModel { get; set; }

        /// <summary>
        /// Name of the Alarm.
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Optional description of the Alarm.
        /// </summary>
        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        /// <summary>
        /// Id of the Container which the Alarm is found in.
        /// </summary>
        [JsonProperty(PropertyName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
