﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// Describes how an Alarm will be patched.
    /// </summary>
    public partial class UpdateAlarmModel
    {
        /// <summary>
        /// Initializes a new instance of the UpdateAlarmModel class.
        /// </summary>
        public UpdateAlarmModel() { }

        /// <summary>
        /// Initializes a new instance of the UpdateAlarmModel class.
        /// </summary>
        public UpdateAlarmModel(string name = default(string), string description = default(string), string parentId = default(string), string id = default(string))
        {
            Name = name;
            Description = description;
            ParentId = parentId;
            Id = id;
        }

        /// <summary>
        /// Name of the Alarm.
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Optional description of the Alarm.
        /// </summary>
        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        /// <summary>
        /// Id of the Container which the Alarm is found in.
        /// </summary>
        [JsonProperty(PropertyName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
