using System;
using System.Collections.Generic;
using System.Linq;
using Ews.Server.Contract;
using Mongoose.Ews.Server.Data;
using NLog;

namespace SmartConnector.OpcUaExtension.EwsServer
{
    /// <summary>
    /// Custom processor for handling SetValues requests in the OPC UA Extension
    /// </summary>
    public class CustomSetValuesProcessor
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private readonly EwsServerDataAdapter _dataAdapter;

        #region Constructor
        public CustomSetValuesProcessor(EwsServerDataAdapter dataAdapter)
        {
            _dataAdapter = dataAdapter ?? throw new ArgumentNullException(nameof(dataAdapter));
        }
        #endregion

        #region ProcessSetValues
        public SetValuesResponse ProcessSetValues(SetValuesRequest request)
        {
            var results = new List<SetValueResult>();

            if (request.ItemValues == null || request.ItemValues.Count == 0)
            {
                return new SetValuesResponse
                {
                    Success = true,
                    ItemResults = results.ToArray()
                };
            }

            foreach (var itemValue in request.ItemValues)
            {
                try
                {
                    var result = ProcessSingleSetValue(itemValue);
                    results.Add(result);
                }
                catch (Exception ex)
                {
                    Logger.Error(ex, $"Error processing SetValue for {itemValue.AlternateId}");
                    results.Add(new SetValueResult
                    {
                        AlternateId = itemValue.AlternateId,
                        Success = false,
                        ErrorMessage = ex.Message
                    });
                }
            }

            var overallSuccess = results.All(r => r.Success);
            return new SetValuesResponse
            {
                Success = overallSuccess,
                ItemResults = results.ToArray(),
                ErrorMessage = overallSuccess ? null : "One or more set value operations failed"
            };
        }
        #endregion

        #region ProcessSingleSetValue
        private SetValueResult ProcessSingleSetValue(ItemValue itemValue)
        {
            Logger.Debug($"Processing SetValue for {itemValue.AlternateId} = {itemValue.Value}");

            // Find the value item
            var valueItem = _dataAdapter.ValueItems.FirstOrDefault(vi => vi.AlternateId == itemValue.AlternateId);
            if (valueItem == null)
            {
                return new SetValueResult
                {
                    AlternateId = itemValue.AlternateId,
                    Success = false,
                    ErrorMessage = $"Value item with AlternateId '{itemValue.AlternateId}' not found"
                };
            }

            // Check if the item is writeable
            if (valueItem.Writeable == Ews.Common.EwsValueWriteableEnum.ReadOnly)
            {
                return new SetValueResult
                {
                    AlternateId = itemValue.AlternateId,
                    Success = false,
                    ErrorMessage = "Value item is read-only"
                };
            }

            try
            {
                // Handle special cases for OPC UA configuration items
                if (IsOpcUaConfigurationItem(itemValue.AlternateId))
                {
                    return ProcessOpcUaConfigurationSetValue(valueItem, itemValue);
                }

                // For regular items, use the standard EWS mechanism
                var state = itemValue.State ?? Ews.Common.EwsValueStateEnum.Good;
                _dataAdapter.ModifyValueItemValue(valueItem, itemValue.Value, state);

                Logger.Debug($"Successfully set {itemValue.AlternateId} = {itemValue.Value}");
                return new SetValueResult
                {
                    AlternateId = itemValue.AlternateId,
                    Success = true
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error setting value for {itemValue.AlternateId}");
                return new SetValueResult
                {
                    AlternateId = itemValue.AlternateId,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
        #endregion

        #region IsOpcUaConfigurationItem
        private bool IsOpcUaConfigurationItem(string alternateId)
        {
            return alternateId == OpcUaHelper.OpcUaEndpointId ||
                   alternateId.StartsWith("NodeMapping_") && alternateId.EndsWith("_NodeId");
        }
        #endregion

        #region ProcessOpcUaConfigurationSetValue
        private SetValueResult ProcessOpcUaConfigurationSetValue(Mongoose.Ews.Server.Data.EwsValueItem valueItem, ItemValue itemValue)
        {
            try
            {
                // Validate the value based on the configuration type
                if (itemValue.AlternateId == OpcUaHelper.OpcUaEndpointId)
                {
                    // Validate OPC UA endpoint URL format
                    if (!IsValidOpcUaEndpoint(itemValue.Value?.ToString()))
                    {
                        return new SetValueResult
                        {
                            AlternateId = itemValue.AlternateId,
                            Success = false,
                            ErrorMessage = "Invalid OPC UA endpoint URL format. Expected format: opc.tcp://hostname:port"
                        };
                    }
                }
                else if (itemValue.AlternateId.EndsWith("_NodeId"))
                {
                    // Validate OPC UA Node ID format
                    if (!string.IsNullOrEmpty(itemValue.Value?.ToString()) && !OpcUaHelper.IsValidNodeId(itemValue.Value.ToString()))
                    {
                        return new SetValueResult
                        {
                            AlternateId = itemValue.AlternateId,
                            Success = false,
                            ErrorMessage = "Invalid OPC UA Node ID format. Examples: ns=2;s=MyNode, ns=1;i=1234"
                        };
                    }
                }

                // If validation passes, set the value
                var state = itemValue.State ?? Ews.Common.EwsValueStateEnum.Good;
                _dataAdapter.ModifyValueItemValue(valueItem, itemValue.Value, state);

                Logger.Info($"OPC UA configuration updated: {itemValue.AlternateId} = {itemValue.Value}");
                return new SetValueResult
                {
                    AlternateId = itemValue.AlternateId,
                    Success = true
                };
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error setting OPC UA configuration value for {itemValue.AlternateId}");
                return new SetValueResult
                {
                    AlternateId = itemValue.AlternateId,
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }
        #endregion

        #region IsValidOpcUaEndpoint
        private bool IsValidOpcUaEndpoint(string endpoint)
        {
            if (string.IsNullOrEmpty(endpoint)) return false;
            
            try
            {
                var uri = new Uri(endpoint);
                return uri.Scheme.Equals("opc.tcp", StringComparison.OrdinalIgnoreCase) ||
                       uri.Scheme.Equals("http", StringComparison.OrdinalIgnoreCase) ||
                       uri.Scheme.Equals("https", StringComparison.OrdinalIgnoreCase);
            }
            catch
            {
                return false;
            }
        }
        #endregion
    }
}
