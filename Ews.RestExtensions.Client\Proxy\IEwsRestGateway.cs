﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// </summary>
    public partial interface IEwsRestGateway : IDisposable
    {
        /// <summary>
        /// The base URI of the service.
        /// </summary>
        Uri BaseUri { get; set; }

        /// <summary>
        /// Gets or sets json serialization settings.
        /// </summary>
        JsonSerializerSettings SerializationSettings { get; }

        /// <summary>
        /// Gets or sets json deserialization settings.
        /// </summary>
        JsonSerializerSettings DeserializationSettings { get; }

        /// <summary>
        /// Subscription credentials which uniquely identify client
        /// subscription.
        /// </summary>
        ServiceClientCredentials Credentials { get; }


        /// <summary>
        /// Gets the IAlarmEvents.
        /// </summary>
        IAlarmEvents AlarmEvents { get; }

        /// <summary>
        /// Gets the IAlarmEventTypes.
        /// </summary>
        IAlarmEventTypes AlarmEventTypes { get; }

        /// <summary>
        /// Gets the IAlarms.
        /// </summary>
        IAlarms Alarms { get; }

        /// <summary>
        /// Gets the IContainers.
        /// </summary>
        IContainers Containers { get; }

        /// <summary>
        /// Gets the INotifications.
        /// </summary>
        INotifications Notifications { get; }

        /// <summary>
        /// Gets the IRoot.
        /// </summary>
        IRoot Root { get; }

        /// <summary>
        /// Gets the ISubscriptions.
        /// </summary>
        ISubscriptions Subscriptions { get; }

        /// <summary>
        /// Gets the ISystemEvents.
        /// </summary>
        ISystemEvents SystemEvents { get; }

        /// <summary>
        /// Gets the ISystemEventTypes.
        /// </summary>
        ISystemEventTypes SystemEventTypes { get; }

        /// <summary>
        /// Gets the ITrends.
        /// </summary>
        ITrends Trends { get; }

        /// <summary>
        /// Gets the ITrendSamples.
        /// </summary>
        ITrendSamples TrendSamples { get; }

        /// <summary>
        /// Gets the IValues.
        /// </summary>
        IValues Values { get; }

    }
}
