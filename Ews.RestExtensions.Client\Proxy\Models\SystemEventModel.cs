﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A SystemEvent represents the occurrence of a server specific event.
    /// </summary>
    public partial class SystemEventModel
    {
        /// <summary>
        /// Initializes a new instance of the SystemEventModel class.
        /// </summary>
        public SystemEventModel() { }

        /// <summary>
        /// Initializes a new instance of the SystemEventModel class.
        /// </summary>
        public SystemEventModel(string type = default(string), string message = default(string), DateTime? occurredOn = default(DateTime?), int? priority = default(int?), string sourceId = default(string), string sourceName = default(string), string id = default(string))
        {
            Type = type;
            Message = message;
            OccurredOn = occurredOn;
            Priority = priority;
            SourceId = sourceId;
            SourceName = sourceName;
            Id = id;
        }

        /// <summary>
        /// Server specific type of the SystemEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Human readable message for the SystemEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Message")]
        public string Message { get; set; }

        /// <summary>
        /// DateTime when the SystemEvent was generated.
        /// </summary>
        [JsonProperty(PropertyName = "OccurredOn")]
        public DateTime? OccurredOn { get; set; }

        /// <summary>
        /// Priority value of the SystemEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Priority")]
        public int? Priority { get; set; }

        /// <summary>
        /// Id for a server specific "source" of the SystemEvent.
        /// </summary>
        [JsonProperty(PropertyName = "SourceId")]
        public string SourceId { get; set; }

        /// <summary>
        /// Name for the server specific "source" of the SystemEvent.
        /// </summary>
        [JsonProperty(PropertyName = "SourceName")]
        public string SourceName { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
