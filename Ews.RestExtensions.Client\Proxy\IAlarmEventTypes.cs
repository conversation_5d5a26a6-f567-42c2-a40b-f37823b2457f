﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// AlarmEventTypes operations.
    /// </summary>
    public partial interface IAlarmEventTypes
    {
        /// <summary>
        /// Creates a new AlarmEventType and returns it.
        /// </summary>
        /// <param name='newItem'>
        /// Definition of how the AlarmEventType will be created.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmEventTypeModel>> CreateWithHttpMessagesAsync(NewAlarmEventTypeModel newItem, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns all AlarmEventTypes which fit the filter criteria
        /// supplied.  All filter parameters are optional.  AlarmEventTypes
        /// will be ordered and paged as requested.
        /// </summary>
        /// <param name='name'>
        /// Return only AlarmEventType whose name contains this value.
        /// </param>
        /// <param name='orderBy'>
        /// AlarmEventType will be returned in this order.  NameAscending by
        /// default. Possible values include: 'NameAscending',
        /// 'NameDescending'
        /// </param>
        /// <param name='take'>
        /// Number of AlarmEventType that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of AlarmEvent that should be skipped before items are
        /// returned.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<AlarmEventTypeModel>>> RetrieveWithHttpMessagesAsync(string name = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the AlarmEventType with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmEventTypeModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Description property for the AlarmEventType with the
        /// Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType whose Description property is to be
        /// returned.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> GetDescriptionWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Name property for the AlarmEventType with the Id
        /// provided to the new value supplied.  Returns the modified
        /// AlarmEventType.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType whose Name property is to be modified.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Description value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmEventTypeModel>> UpdateDescriptionWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Name property for the AlarmEventType with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType whose Name property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> GetNameWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Name property for the AlarmEventType with the Id
        /// provided to the new value supplied.  Returns the modified
        /// AlarmEventType.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType whose Name property is to be modified.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Name value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmEventTypeModel>> UpdateNameWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the AlarmEventType with the Id provided to the values
        /// supplied.  Returns the modified AlarmEventType.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType to be modified.  Must be double URL
        /// encoded.
        /// </param>
        /// <param name='newValues'>
        /// Values to update the AlarmEventType with.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmEventTypeModel>> UpdateWithHttpMessagesAsync(string id, UpdateAlarmEventTypeModel newValues, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Deletes the AlarmEventType with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEventType to be deleted.  Must be double URL
        /// encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> DeleteWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
