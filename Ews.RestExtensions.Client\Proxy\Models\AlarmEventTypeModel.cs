﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// An AlarmEventType represents a server specific a Type for an
    /// AlarmEvent.
    /// </summary>
    public partial class AlarmEventTypeModel
    {
        /// <summary>
        /// Initializes a new instance of the AlarmEventTypeModel class.
        /// </summary>
        public AlarmEventTypeModel() { }

        /// <summary>
        /// Initializes a new instance of the AlarmEventTypeModel class.
        /// </summary>
        public AlarmEventTypeModel(string name = default(string), string description = default(string), string id = default(string))
        {
            Name = name;
            Description = description;
            Id = id;
        }

        /// <summary>
        /// Name of the AlarmEventType.
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Optional description of the AlarmEventType.
        /// </summary>
        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
