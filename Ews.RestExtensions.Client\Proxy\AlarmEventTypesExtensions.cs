﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for AlarmEventTypes.
    /// </summary>
    public static partial class AlarmEventTypesExtensions
    {
            /// <summary>
            /// Creates a new AlarmEventType and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the AlarmEventType will be created.
            /// </param>
            public static AlarmEventTypeModel Create(this IAlarmEventTypes operations, NewAlarmEventTypeModel newItem)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).CreateAsync(newItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Creates a new AlarmEventType and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the AlarmEventType will be created.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmEventTypeModel> CreateAsync(this IAlarmEventTypes operations, NewAlarmEventTypeModel newItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(newItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all AlarmEventTypes which fit the filter criteria supplied.  All
            /// filter parameters are optional.  AlarmEventTypes will be ordered and
            /// paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only AlarmEventType whose name contains this value.
            /// </param>
            /// <param name='orderBy'>
            /// AlarmEventType will be returned in this order.  NameAscending by default.
            /// Possible values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of AlarmEventType that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of AlarmEvent that should be skipped before items are returned.
            /// </param>
            public static IList<AlarmEventTypeModel> Retrieve(this IAlarmEventTypes operations, string name = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).RetrieveAsync(name, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all AlarmEventTypes which fit the filter criteria supplied.  All
            /// filter parameters are optional.  AlarmEventTypes will be ordered and
            /// paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only AlarmEventType whose name contains this value.
            /// </param>
            /// <param name='orderBy'>
            /// AlarmEventType will be returned in this order.  NameAscending by default.
            /// Possible values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of AlarmEventType that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of AlarmEvent that should be skipped before items are returned.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<AlarmEventTypeModel>> RetrieveAsync(this IAlarmEventTypes operations, string name = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(name, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the AlarmEventType with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType requested.  Must be double URL encoded.
            /// </param>
            public static AlarmEventTypeModel RetrieveById(this IAlarmEventTypes operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the AlarmEventType with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmEventTypeModel> RetrieveByIdAsync(this IAlarmEventTypes operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Description property for the AlarmEventType with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Description property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            public static string GetDescription(this IAlarmEventTypes operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).GetDescriptionAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Description property for the AlarmEventType with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Description property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> GetDescriptionAsync(this IAlarmEventTypes operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.GetDescriptionWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Name property for the AlarmEventType with the Id provided to
            /// the new value supplied.  Returns the modified AlarmEventType.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Name property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            public static AlarmEventTypeModel UpdateDescription(this IAlarmEventTypes operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).UpdateDescriptionAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Name property for the AlarmEventType with the Id provided to
            /// the new value supplied.  Returns the modified AlarmEventType.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Name property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmEventTypeModel> UpdateDescriptionAsync(this IAlarmEventTypes operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateDescriptionWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Name property for the AlarmEventType with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Name property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string GetName(this IAlarmEventTypes operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).GetNameAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Name property for the AlarmEventType with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Name property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> GetNameAsync(this IAlarmEventTypes operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.GetNameWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Name property for the AlarmEventType with the Id provided to
            /// the new value supplied.  Returns the modified AlarmEventType.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Name property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value.
            /// </param>
            public static AlarmEventTypeModel UpdateName(this IAlarmEventTypes operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).UpdateNameAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Name property for the AlarmEventType with the Id provided to
            /// the new value supplied.  Returns the modified AlarmEventType.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType whose Name property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmEventTypeModel> UpdateNameAsync(this IAlarmEventTypes operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateNameWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the AlarmEventType with the Id provided to the values supplied.
            /// Returns the modified AlarmEventType.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='newValues'>
            /// Values to update the AlarmEventType with.
            /// </param>
            public static AlarmEventTypeModel Update(this IAlarmEventTypes operations, string id, UpdateAlarmEventTypeModel newValues)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).UpdateAsync(id, newValues), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the AlarmEventType with the Id provided to the values supplied.
            /// Returns the modified AlarmEventType.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='newValues'>
            /// Values to update the AlarmEventType with.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmEventTypeModel> UpdateAsync(this IAlarmEventTypes operations, string id, UpdateAlarmEventTypeModel newValues, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateWithHttpMessagesAsync(id, newValues, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes the AlarmEventType with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType to be deleted.  Must be double URL encoded.
            /// </param>
            public static object Delete(this IAlarmEventTypes operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEventTypes)s).DeleteAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes the AlarmEventType with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEventType to be deleted.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteAsync(this IAlarmEventTypes operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
