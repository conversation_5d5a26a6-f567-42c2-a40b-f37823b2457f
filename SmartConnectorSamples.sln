﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 15
VisualStudioVersion = 15.0.27130.2027
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartConnector.WeatherExtension", "SmartConnector.WeatherExtension\SmartConnector.WeatherExtension.csproj", "{ED1CFC48-1815-44D0-8EBB-AD6FE6CAEC2F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartConnector.WeatherExtension.Test", "SmartConnector.WeatherExtension.Test\SmartConnector.WeatherExtension.Test.csproj", "{5BD9B6A1-9266-4FEB-AA99-0AEBD3213641}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ews.RestExtensions.Client", "Ews.RestExtensions.Client\Ews.RestExtensions.Client.csproj", "{AB219EE4-9A51-4C99-967C-2B85970C68AF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "Ews.RestExtensions.Client.Test", "Ews.RestExtensions.Client.Test\Ews.RestExtensions.Client.Test.csproj", "{8DE84D38-1EE4-4D20-9639-C57C333ABAA8}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartConnector.UtilityExtensions", "SmartConnector.UtilityExtensions\SmartConnector.UtilityExtensions.csproj", "{773AB1A2-BCC9-47E1-81B5-D70DBE87A3E3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SmartConnector.UtilityExtensions.Test", "SmartConnector.UtilityExtensions.Test\SmartConnector.UtilityExtensions.Test.csproj", "{EC4B2FE1-B5CD-4057-8501-BBCD5ECA4464}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CustomRestExtension.Test", "CustomRestExtension.Test\CustomRestExtension.Test.csproj", "{D6BF928D-BEB0-4A53-9648-23AAE5680F53}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CustomRestExtension", "CustomRestExtension\CustomRestExtension.csproj", "{CE7D312B-3236-4293-9FC9-253F15FAE3D3}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "CustomSoapProvider", "CustomSoapProvider\CustomSoapProvider.csproj", "{1E60B1E5-8A6F-4EAA-9B12-42206CC3BC33}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{ED1CFC48-1815-44D0-8EBB-AD6FE6CAEC2F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{ED1CFC48-1815-44D0-8EBB-AD6FE6CAEC2F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{ED1CFC48-1815-44D0-8EBB-AD6FE6CAEC2F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{ED1CFC48-1815-44D0-8EBB-AD6FE6CAEC2F}.Release|Any CPU.Build.0 = Release|Any CPU
		{5BD9B6A1-9266-4FEB-AA99-0AEBD3213641}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{5BD9B6A1-9266-4FEB-AA99-0AEBD3213641}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{5BD9B6A1-9266-4FEB-AA99-0AEBD3213641}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{5BD9B6A1-9266-4FEB-AA99-0AEBD3213641}.Release|Any CPU.Build.0 = Release|Any CPU
		{AB219EE4-9A51-4C99-967C-2B85970C68AF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{AB219EE4-9A51-4C99-967C-2B85970C68AF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{AB219EE4-9A51-4C99-967C-2B85970C68AF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{AB219EE4-9A51-4C99-967C-2B85970C68AF}.Release|Any CPU.Build.0 = Release|Any CPU
		{8DE84D38-1EE4-4D20-9639-C57C333ABAA8}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{8DE84D38-1EE4-4D20-9639-C57C333ABAA8}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{8DE84D38-1EE4-4D20-9639-C57C333ABAA8}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{8DE84D38-1EE4-4D20-9639-C57C333ABAA8}.Release|Any CPU.Build.0 = Release|Any CPU
		{773AB1A2-BCC9-47E1-81B5-D70DBE87A3E3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{773AB1A2-BCC9-47E1-81B5-D70DBE87A3E3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{773AB1A2-BCC9-47E1-81B5-D70DBE87A3E3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{773AB1A2-BCC9-47E1-81B5-D70DBE87A3E3}.Release|Any CPU.Build.0 = Release|Any CPU
		{EC4B2FE1-B5CD-4057-8501-BBCD5ECA4464}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{EC4B2FE1-B5CD-4057-8501-BBCD5ECA4464}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{EC4B2FE1-B5CD-4057-8501-BBCD5ECA4464}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{EC4B2FE1-B5CD-4057-8501-BBCD5ECA4464}.Release|Any CPU.Build.0 = Release|Any CPU
		{D6BF928D-BEB0-4A53-9648-23AAE5680F53}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{D6BF928D-BEB0-4A53-9648-23AAE5680F53}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{D6BF928D-BEB0-4A53-9648-23AAE5680F53}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{D6BF928D-BEB0-4A53-9648-23AAE5680F53}.Release|Any CPU.Build.0 = Release|Any CPU
		{CE7D312B-3236-4293-9FC9-253F15FAE3D3}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{CE7D312B-3236-4293-9FC9-253F15FAE3D3}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{CE7D312B-3236-4293-9FC9-253F15FAE3D3}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{CE7D312B-3236-4293-9FC9-253F15FAE3D3}.Release|Any CPU.Build.0 = Release|Any CPU
		{1E60B1E5-8A6F-4EAA-9B12-42206CC3BC33}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{1E60B1E5-8A6F-4EAA-9B12-42206CC3BC33}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{1E60B1E5-8A6F-4EAA-9B12-42206CC3BC33}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{1E60B1E5-8A6F-4EAA-9B12-42206CC3BC33}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {4125AD62-C9E3-4406-8805-35FF603C10D3}
	EndGlobalSection
EndGlobal
