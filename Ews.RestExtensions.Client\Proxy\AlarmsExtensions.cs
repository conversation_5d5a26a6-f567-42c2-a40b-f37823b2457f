﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for Alarms.
    /// </summary>
    public static partial class AlarmsExtensions
    {
            /// <summary>
            /// Creates a new Alarm and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Alarm will be created.
            /// </param>
            public static AlarmModel Create(this IAlarms operations, NewAlarmModel newItem)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).CreateAsync(newItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Creates a new Alarm and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Alarm will be created.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> CreateAsync(this IAlarms operations, NewAlarmModel newItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(newItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all Alarms which fit the filter criteria supplied.  All filter
            /// parameters are optional.  Alarms will be ordered and paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only Alarm whose name contains this value.
            /// </param>
            /// <param name='state'>
            /// Return only Alarm with this State value. Possible values include:
            /// 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
            /// </param>
            /// <param name='orderBy'>
            /// Alarm will be returned in this order.  TransitionedAtAscending by default.
            /// Possible values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Alarm that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Alarm that should be skipped before items are returned.  0 by
            /// default.
            /// </param>
            public static IList<AlarmModel> Retrieve(this IAlarms operations, string name = default(string), string state = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveAsync(name, state, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all Alarms which fit the filter criteria supplied.  All filter
            /// parameters are optional.  Alarms will be ordered and paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only Alarm whose name contains this value.
            /// </param>
            /// <param name='state'>
            /// Return only Alarm with this State value. Possible values include:
            /// 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
            /// </param>
            /// <param name='orderBy'>
            /// Alarm will be returned in this order.  TransitionedAtAscending by default.
            /// Possible values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Alarm that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Alarm that should be skipped before items are returned.  0 by
            /// default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<AlarmModel>> RetrieveAsync(this IAlarms operations, string name = default(string), string state = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(name, state, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm requested.  Must be double URL encoded.
            /// </param>
            public static AlarmModel RetrieveById(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> RetrieveByIdAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Description property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Description property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveDescription(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveDescriptionAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Description property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Description property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveDescriptionAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveDescriptionWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Description property for the Alarm with the Id provided to
            /// the new value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Description property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            public static AlarmModel UpdateDescription(this IAlarms operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).UpdateDescriptionAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Description property for the Alarm with the Id provided to
            /// the new value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Description property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> UpdateDescriptionAsync(this IAlarms operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateDescriptionWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Name property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Name property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            public static string RetrieveName(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveNameAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Name property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Name property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveNameAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveNameWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Name property for the Alarm with the Id provided to the new
            /// value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Name property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value.
            /// </param>
            public static AlarmModel UpdateName(this IAlarms operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).UpdateNameAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Name property for the Alarm with the Id provided to the new
            /// value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Name property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> UpdateNameAsync(this IAlarms operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateNameWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the parent Container for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose parent Container is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static ContainerModel RetrieveParent(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveParentAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the parent Container for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose parent Container is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> RetrieveParentAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveParentWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the ParentId property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose ParentId property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveParentId(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveParentIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the ParentId property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose ParentId property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveParentIdAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveParentIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the ParentId property for the Alarm with the Id provided to the
            /// new value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose ParentId property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New ParentId value
            /// </param>
            public static AlarmModel UpdateParentId(this IAlarms operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).UpdateParentIdAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the ParentId property for the Alarm with the Id provided to the
            /// new value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose ParentId property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New ParentId value
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> UpdateParentIdAsync(this IAlarms operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateParentIdWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the State property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose State property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveState(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveStateAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the State property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose State property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveStateAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveStateWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the TransitionModel property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose TransitionModel property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveTransitionModel(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveTransitionModelAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the TransitionModel property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose TransitionModel property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveTransitionModelAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTransitionModelWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the associated Value for the Alarm with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm who�s associated Value is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static ValueModel RetrieveValue(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveValueAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the associated Value for the Alarm with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm who�s associated Value is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> RetrieveValueAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveValueWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the ValueId property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose ValueId property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveValueId(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).RetrieveValueIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the ValueId property for the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose ValueId property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveValueIdAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveValueIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Alarm with the Id provided to the values supplied.  Returns
            /// the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='updatedItem'>
            /// Item to be updated.  All modfied properties will be changed.
            /// </param>
            public static AlarmModel Update(this IAlarms operations, string id, UpdateAlarmModel updatedItem)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).UpdateAsync(id, updatedItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Alarm with the Id provided to the values supplied.  Returns
            /// the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='updatedItem'>
            /// Item to be updated.  All modfied properties will be changed.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> UpdateAsync(this IAlarms operations, string id, UpdateAlarmModel updatedItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateWithHttpMessagesAsync(id, updatedItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Id property for the Alarm with the Id provided to the new
            /// value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Id property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Id value.
            /// </param>
            public static string UpdateId(this IAlarms operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).UpdateIdAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Id property for the Alarm with the Id provided to the new
            /// value supplied.  Returns the modified Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm whose Id property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Id value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> UpdateIdAsync(this IAlarms operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateIdWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Executes an Acknowledge action on the Alarm with the Id provided.  The
            /// result will generate a new AlarmEvent based on the information contained
            /// in the newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            public static AlarmModel Acknowledge(this IAlarms operations, string id, AlarmEventInfo newEvent)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).AcknowledgeAsync(id, newEvent), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Executes an Acknowledge action on the Alarm with the Id provided.  The
            /// result will generate a new AlarmEvent based on the information contained
            /// in the newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> AcknowledgeAsync(this IAlarms operations, string id, AlarmEventInfo newEvent, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.AcknowledgeWithHttpMessagesAsync(id, newEvent, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Executes an Activate action on the Alarm with the Id provided.  The result
            /// will generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            public static AlarmModel Activate(this IAlarms operations, string id, AlarmEventInfo newEvent)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).ActivateAsync(id, newEvent), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Executes an Activate action on the Alarm with the Id provided.  The result
            /// will generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> ActivateAsync(this IAlarms operations, string id, AlarmEventInfo newEvent, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.ActivateWithHttpMessagesAsync(id, newEvent, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Executes a Disable action on the Alarm with the Id provided.  The result
            /// will generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            public static AlarmModel Disable(this IAlarms operations, string id, AlarmEventInfo newEvent)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).DisableAsync(id, newEvent), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Executes a Disable action on the Alarm with the Id provided.  The result
            /// will generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> DisableAsync(this IAlarms operations, string id, AlarmEventInfo newEvent, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DisableWithHttpMessagesAsync(id, newEvent, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Executes a Reset action on the Alarm with the Id provided.  The result
            /// will generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            public static AlarmModel Reset(this IAlarms operations, string id, AlarmEventInfo newEvent)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).ResetAsync(id, newEvent), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Executes a Reset action on the Alarm with the Id provided.  The result
            /// will generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> ResetAsync(this IAlarms operations, string id, AlarmEventInfo newEvent, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.ResetWithHttpMessagesAsync(id, newEvent, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Executes a manual state change action on the Alarm with the Id provided
            /// (Alarm must have an Unrestricted TransitionModel).  The result will
            /// generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            public static AlarmModel ChangeState(this IAlarms operations, string id, AlarmEventStateChangeInfo newEvent)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).ChangeStateAsync(id, newEvent), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Executes a manual state change action on the Alarm with the Id provided
            /// (Alarm must have an Unrestricted TransitionModel).  The result will
            /// generate a new AlarmEvent based on the information contained in the
            /// newEvent parameter.
            /// Restrictions apply based on the TransitionModel of the Alarm.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
            /// </param>
            /// <param name='newEvent'>
            /// Information to describe the generated AlarmEvent.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> ChangeStateAsync(this IAlarms operations, string id, AlarmEventStateChangeInfo newEvent, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.ChangeStateWithHttpMessagesAsync(id, newEvent, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be deleted.  Must be double URL encoded.
            /// </param>
            public static object Delete(this IAlarms operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarms)s).DeleteAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes the Alarm with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Alarm to be deleted.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteAsync(this IAlarms operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
