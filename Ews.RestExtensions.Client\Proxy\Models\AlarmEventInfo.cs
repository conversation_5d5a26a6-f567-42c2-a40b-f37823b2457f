﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// Represents the information needed when generating an AlarmEvent for an
    /// Alarm.
    /// </summary>
    public partial class AlarmEventInfo
    {
        /// <summary>
        /// Initializes a new instance of the AlarmEventInfo class.
        /// </summary>
        public AlarmEventInfo() { }

        /// <summary>
        /// Initializes a new instance of the AlarmEventInfo class.
        /// </summary>
        public AlarmEventInfo(string message = default(string), string type = default(string), int? priority = default(int?))
        {
            Message = message;
            Type = type;
            Priority = priority;
        }

        /// <summary>
        /// The Message to be given with the generated AlarmEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Message")]
        public string Message { get; set; }

        /// <summary>
        /// The Type of to be given with the generated AlarmEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// The Priority to be given to the genreated AlarmEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Priority")]
        public int? Priority { get; set; }

    }
}
