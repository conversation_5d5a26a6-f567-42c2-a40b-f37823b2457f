﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// AlarmEvents operations.
    /// </summary>
    public partial interface IAlarmEvents
    {
        /// <summary>
        /// Returns all AlarmEvents which fit the filter criteria supplied.
        /// All filter parameters are optional.  AlarmEvents will be ordered
        /// and paged as requested.
        /// </summary>
        /// <param name='alarmId'>
        /// Return only AlarmEvent for the referenced Alarm.
        /// </param>
        /// <param name='priorityFrom'>
        /// Return only AlarmEvent with a Priority greater or equal to the
        /// value supplied.
        /// </param>
        /// <param name='priorityTo'>
        /// Return only AlarmEvent with a Priority less or equal to the value
        /// supplied.
        /// </param>
        /// <param name='state'>
        /// Return only AlarmEvent with this State value. Possible values
        /// include: 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
        /// </param>
        /// <param name='acknowledgeable'>
        /// Return only AlarmEvent with this Acknowledgeable value. Possible
        /// values include: 'No', 'Yes', 'Required'
        /// </param>
        /// <param name='type'>
        /// Return only AlarmEvent with this Type value.
        /// </param>
        /// <param name='originallyAlarmedOnOrAfter'>
        /// Return only AlarmEvent with OccuredOn which is on or after this
        /// value.
        /// </param>
        /// <param name='originallyAlarmedBefore'>
        /// Return only AlarmEvent with OccuredOn which is before this value.
        /// </param>
        /// <param name='transitionedOnOrAfter'>
        /// Return only AlarmEvent with LastTransitionedOn which is on or
        /// after this value.
        /// </param>
        /// <param name='transitionedBefore'>
        /// Return only AlarmEvent with LastTransitionedOn which is before
        /// this value.
        /// </param>
        /// <param name='orderBy'>
        /// AlarmEvent will be returned in this order.
        /// TransitionedAtAscending by default. Possible values include:
        /// 'TransitionedAtAscending', 'TransitionedAtDescending',
        /// 'OriginallyAlarmedAtAscending', 'OriginallyAlarmedAtDescending'
        /// </param>
        /// <param name='take'>
        /// Number of AlarmEvent that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of AlarmEvent that should be skipped before items are
        /// returned.  0 by default.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<AlarmEventModel>>> RetrieveWithHttpMessagesAsync(string alarmId = default(string), int? priorityFrom = default(int?), int? priorityTo = default(int?), string state = default(string), string acknowledgeable = default(string), string type = default(string), DateTime? originallyAlarmedOnOrAfter = default(DateTime?), DateTime? originallyAlarmedBefore = default(DateTime?), DateTime? transitionedOnOrAfter = default(DateTime?), DateTime? transitionedBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the AlarmEvent with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmEventModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the AlarmId property for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose AlarmId property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveAlarmIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the referenced Alarm for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent for which the referenced Alarm is to be
        /// returned.  Value must be double URL encoded
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> RetrieveAlarmWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Acknowledgeable property for the AlarmEvent with the
        /// Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose Acknowledgeable property is to be
        /// returned.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveAcknowledgeableWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the OccurredOn property for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose OccurredOn property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<DateTime?>> RetrieveOccurredOnWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the LastTransitionedOn property for the AlarmEvent with
        /// the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose LastTransitionedOn property is to be
        /// returned.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<DateTime?>> RetrieveLastTransitionedOnWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Message property for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose Message property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveMessageWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Priority property for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose Priority property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<int?>> RetrievePriorityWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the State property for the AlarmEvent with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose State property is to be returned.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveStateWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Type property for the AlarmEvent with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent whose Type property is to be returned.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveTypeWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Executes an Acknowledge operation on the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the AlarmEvent to acknowledge.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> AcknowledgeWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
