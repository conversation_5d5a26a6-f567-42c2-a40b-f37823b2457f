﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// SystemEventTypes operations.
    /// </summary>
    public partial interface ISystemEventTypes
    {
        /// <summary>
        /// Returns all SystemEventType which fit the filter criteria
        /// supplied. All filter parameters are optional. SystemEventType
        /// will be ordered and paged as requested.
        /// </summary>
        /// <param name='name'>
        /// Filtered by Name.
        /// </param>
        /// <param name='orderBy'>
        /// SystemEventType will be returned in this order.  NameAscending by
        /// default. Possible values include: 'NameAscending',
        /// 'NameDescending'
        /// </param>
        /// <param name='take'>
        /// Number of SystemEventType that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of SystemEventType that should be skipped before items are
        /// returned.  0 by default.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<SystemEventTypeModel>>> RetrieveWithHttpMessagesAsync(string name = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the SystemEventType with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the SystemEventType requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<SystemEventTypeModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Description property for the SystemEventType with the
        /// Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the SystemEventType whose Description property is to be
        /// returned.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveDescriptionWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Name property for the SystemEventType with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the SystemEventType whose Name property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveNameWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
