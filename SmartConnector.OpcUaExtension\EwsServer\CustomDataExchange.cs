using System;
using System.ComponentModel;
using System.Linq;
using Mongoose.Ews.Server.Data;
using NLog;

namespace SmartConnector.OpcUaExtension.EwsServer
{
    /// <summary>
    /// Custom data exchange component for OPC UA Extension
    /// </summary>
    [ToolboxItem(false)]
    public partial class CustomDataExchange : Component
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();
        private EwsServerDataAdapter _dataAdapter;

        #region Constructor
        public CustomDataExchange()
        {
            InitializeComponent();
            Logger.Debug("CustomDataExchange component created");
        }

        public CustomDataExchange(IContainer container)
        {
            container.Add(this);
            InitializeComponent();
            Logger.Debug("CustomDataExchange component created with container");
        }
        #endregion

        #region DataAdapter Property
        public EwsServerDataAdapter DataAdapter
        {
            get { return _dataAdapter; }
            set
            {
                if (_dataAdapter != value)
                {
                    if (_dataAdapter != null)
                    {
                        // Unsubscribe from old adapter events
                        UnsubscribeFromDataAdapterEvents();
                    }

                    _dataAdapter = value;

                    if (_dataAdapter != null)
                    {
                        // Subscribe to new adapter events
                        SubscribeToDataAdapterEvents();
                    }
                }
            }
        }
        #endregion

        #region Event Subscription
        private void SubscribeToDataAdapterEvents()
        {
            try
            {
                if (_dataAdapter != null)
                {
                    _dataAdapter.ValueItemValueChanged += OnValueItemValueChanged;
                    _dataAdapter.ValueItemAdded += OnValueItemAdded;
                    _dataAdapter.ValueItemRemoved += OnValueItemRemoved;
                    Logger.Debug("Subscribed to DataAdapter events");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error subscribing to DataAdapter events");
            }
        }

        private void UnsubscribeFromDataAdapterEvents()
        {
            try
            {
                if (_dataAdapter != null)
                {
                    _dataAdapter.ValueItemValueChanged -= OnValueItemValueChanged;
                    _dataAdapter.ValueItemAdded -= OnValueItemAdded;
                    _dataAdapter.ValueItemRemoved -= OnValueItemRemoved;
                    Logger.Debug("Unsubscribed from DataAdapter events");
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error unsubscribing from DataAdapter events");
            }
        }
        #endregion

        #region Event Handlers
        private void OnValueItemValueChanged(object sender, ValueItemValueChangedEventArgs e)
        {
            try
            {
                Logger.Debug($"Value changed: {e.ValueItem.AlternateId} = {e.ValueItem.Value}");
                
                // Handle special OPC UA configuration changes
                HandleOpcUaConfigurationChange(e.ValueItem);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error handling value change for {e.ValueItem.AlternateId}");
            }
        }

        private void OnValueItemAdded(object sender, ValueItemAddedEventArgs e)
        {
            try
            {
                Logger.Debug($"Value item added: {e.ValueItem.AlternateId}");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error handling value item added for {e.ValueItem.AlternateId}");
            }
        }

        private void OnValueItemRemoved(object sender, ValueItemRemovedEventArgs e)
        {
            try
            {
                Logger.Debug($"Value item removed: {e.ValueItem.AlternateId}");
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error handling value item removed for {e.ValueItem.AlternateId}");
            }
        }
        #endregion

        #region OPC UA Configuration Handling
        private void HandleOpcUaConfigurationChange(EwsValueItem valueItem)
        {
            try
            {
                // Handle endpoint URL changes
                if (valueItem.AlternateId == OpcUaHelper.OpcUaEndpointId)
                {
                    Logger.Info($"OPC UA endpoint URL changed to: {valueItem.Value}");
                    // The UpdateProcessor will handle reconnection on next cycle
                }
                // Handle node ID configuration changes
                else if (valueItem.AlternateId.StartsWith("NodeMapping_") && valueItem.AlternateId.EndsWith("_NodeId"))
                {
                    Logger.Info($"OPC UA node mapping changed: {valueItem.AlternateId} = {valueItem.Value}");
                    
                    // Validate the node ID if it's not empty
                    if (!string.IsNullOrEmpty(valueItem.Value) && !OpcUaHelper.IsValidNodeId(valueItem.Value))
                    {
                        Logger.Warn($"Invalid OPC UA Node ID format: {valueItem.Value}");
                        // Set the value state to uncertain to indicate validation issue
                        _dataAdapter.ModifyValueItemValue(valueItem, valueItem.Value, Ews.Common.EwsValueStateEnum.Uncertain);
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.Error(ex, $"Error handling OPC UA configuration change for {valueItem.AlternateId}");
            }
        }
        #endregion

        #region Component Designer Generated Code
        private void InitializeComponent()
        {
            // This method is required for the Component Designer
            // and should not be modified using the code editor.
        }
        #endregion

        #region Dispose
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                UnsubscribeFromDataAdapterEvents();
                Logger.Debug("CustomDataExchange component disposed");
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
