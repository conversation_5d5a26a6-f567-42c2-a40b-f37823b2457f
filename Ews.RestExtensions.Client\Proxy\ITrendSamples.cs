﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// TrendSamples operations.
    /// </summary>
    public partial interface ITrendSamples
    {
        /// <summary>
        /// Returns all TrendSample which fit the filter criteria supplied.
        /// All filter parameters are optional except trendId. TrendSample
        /// will be ordered and paged as requested.
        /// </summary>
        /// <param name='trendId'>
        /// The Id of the Trend which TrendSample are to be returned for.
        /// This parameter is not optional.
        /// </param>
        /// <param name='state'>
        /// Return only TrendSample with this State value. Possible values
        /// include: 'Good', 'Uncertain', 'Forced', 'Offline', 'Error'
        /// </param>
        /// <param name='sampledOnOrAfter'>
        /// Return only TrendSample with SampleDate which is on or after this
        /// value.
        /// </param>
        /// <param name='sampledBefore'>
        /// Return only TrendSample with SampleDate which is before this value.
        /// </param>
        /// <param name='orderBy'>
        /// TrendSample will be returned in this order.  SampleDateAscending
        /// by default. Possible values include: 'SampleDateAscending',
        /// 'SampleDateDescending'
        /// </param>
        /// <param name='take'>
        /// Number of TrendSample that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of TrendSample that should be skipped before items are
        /// returned.  0 by default.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<TrendSampleModel>>> RetrieveWithHttpMessagesAsync(string trendId, string state = default(string), DateTime? sampledOnOrAfter = default(DateTime?), DateTime? sampledBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the TrendSample with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the TrendSample requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<TrendSampleModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the State property for the TrendSample with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the TrendSample whose State property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveStateWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the SampleDate property for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the TrendSample whose SampleDate property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<DateTime?>> RetrieveSampleDateWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the TrendModel for the TrendSample with the supplied Id.
        /// </summary>
        /// <param name='id'>
        /// Id of the TrendSample whose TrendModel is to be returned.
        /// Requires double URL encoding.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<TrendModel>> RetrieveTrendWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the TrendId property for the AlarmEvent with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the TrendSample whose TrendId property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> RetrieveTrendIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Value for the TrendSample with the supplied Id.
        /// </summary>
        /// <param name='id'>
        /// Id of the TrendSample whose Value is to be returned.  Requires
        /// double URL encoding.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveValueWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
