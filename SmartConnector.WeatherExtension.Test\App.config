<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <configSections>
    
    <section name="entityFramework" type="System.Data.Entity.Internal.ConfigFile.EntityFrameworkSection, EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" requirePermission="false" />
  <!-- For more information on Entity Framework configuration, visit http://go.microsoft.com/fwlink/?LinkID=237468 --></configSections>
  <connectionStrings>
    <add name="SmartConnectorDb" providerName="System.Data.SqlClient" connectionString="Data Source=(LocalDb)\MSSQLLocalDB;Initial Catalog=SmartConnector.Service;Integrated Security=SSPI;AttachDBFilename=|DataDirectory|\SmartConnector.mdf" />
    <add name="SmartConnectorUserDb" providerName="System.Data.SqlClient" connectionString="Data Source=(LocalDb)\MSSQLLocalDB;Initial Catalog=SmartConnector.Users;Integrated Security=SSPI;AttachDBFilename=|DataDirectory|\SmartConnectorUser.mdf" />
  </connectionStrings>
  <appSettings>
    <add key="EncryptionKey" value="9523771963EC75B6B7F0CDDCAEA48B3ED9454478BF89042D0C2AC442646DECAD" />
  </appSettings>
  <entityFramework>
    <defaultConnectionFactory type="System.Data.Entity.Infrastructure.LocalDbConnectionFactory, EntityFramework">
      <parameters>
        <parameter value="mssqllocaldb"/>
      </parameters>
    </defaultConnectionFactory>
    <providers>
      <provider invariantName="System.Data.SqlClient" type="System.Data.Entity.SqlServer.SqlProviderServices, EntityFramework.SqlServer"/>
    </providers>
  </entityFramework>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <dependentAssembly>
        <assemblyIdentity name="SxL.Common" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.4.10.0" newVersion="2.4.10.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Mongoose.Common" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.4.10.0" newVersion="2.4.10.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Mongoose.Ews.Server.Data" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.4.10.0" newVersion="2.4.10.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Mongoose.Process" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.3.113.0" newVersion="2.3.113.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ews.Common" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.4.10.0" newVersion="2.4.10.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Newtonsoft.Json" publicKeyToken="30ad4fe6b2a6aeed" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-10.0.0.0" newVersion="10.0.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.OAuth" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Microsoft.Owin.Security.Cookies" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-3.1.0.0" newVersion="3.1.0.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Web.Http" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="System.Net.Http.Formatting" publicKeyToken="31bf3856ad364e35" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-5.2.3.0" newVersion="5.2.3.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Mongoose.Configuration" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.3.113.0" newVersion="2.3.113.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ews.Client" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.3.113.0" newVersion="2.3.113.0"/>
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Ews.Server.Contract" publicKeyToken="223885051a9a1eff" culture="neutral"/>
        <bindingRedirect oldVersion="0.0.0.0-2.4.10.0" newVersion="2.4.10.0"/>
      </dependentAssembly>
    </assemblyBinding>
  </runtime>
<startup><supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.6.1"/></startup></configuration>
