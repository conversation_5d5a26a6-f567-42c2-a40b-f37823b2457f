using System;
using System.Collections.Generic;
using System.Linq;
using Ews.Server.Contract;
using Mongoose.Ews.Server;
using Mongoose.Ews.Server.Data;
using NLog;

namespace SmartConnector.OpcUaExtension.EwsServer
{
    /// <summary>
    /// Custom EWS Service Host for OPC UA Extension
    /// </summary>
    public class CustomEwsServiceHost : EwsServiceHost
    {
        private static readonly Logger Logger = LogManager.GetCurrentClassLogger();

        #region Constructor
        public CustomEwsServiceHost(EwsServerDataAdapter dataAdapter) : base(dataAdapter)
        {
            Logger.Info("CustomEwsServiceHost for OPC UA Extension initialized");
        }
        #endregion

        #region SetValues - Override
        public override SetValuesResponse SetValues(SetValuesRequest request)
        {
            try
            {
                Logger.Debug($"SetValues called with {request.ItemValues?.Count ?? 0} items");

                // Use the custom set values processor
                var processor = new CustomSetValuesProcessor(DataAdapter);
                return processor.ProcessSetValues(request);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in SetValues");
                return new SetValuesResponse
                {
                    Success = false,
                    ErrorMessage = ex.Message,
                    ItemResults = request.ItemValues?.Select(iv => new SetValueResult
                    {
                        AlternateId = iv.AlternateId,
                        Success = false,
                        ErrorMessage = ex.Message
                    }).ToArray()
                };
            }
        }
        #endregion

        #region GetValues - Override
        public override GetValuesResponse GetValues(GetValuesRequest request)
        {
            try
            {
                Logger.Debug($"GetValues called for {request.AlternateIds?.Count ?? 0} items");
                return base.GetValues(request);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in GetValues");
                throw;
            }
        }
        #endregion

        #region Subscribe - Override
        public override SubscribeResponse Subscribe(SubscribeRequest request)
        {
            try
            {
                Logger.Debug($"Subscribe called for {request.AlternateIds?.Count ?? 0} items");
                return base.Subscribe(request);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in Subscribe");
                throw;
            }
        }
        #endregion

        #region GetNotification - Override
        public override GetNotificationResponse GetNotification(GetNotificationRequest request)
        {
            try
            {
                Logger.Debug($"GetNotification called for subscription {request.SubscriptionId}");
                return base.GetNotification(request);
            }
            catch (Exception ex)
            {
                Logger.Error(ex, "Error in GetNotification");
                throw;
            }
        }
        #endregion

        #region Dispose - Override
        protected override void Dispose(bool disposing)
        {
            if (disposing)
            {
                Logger.Info("CustomEwsServiceHost for OPC UA Extension disposing");
            }
            base.Dispose(disposing);
        }
        #endregion
    }
}
