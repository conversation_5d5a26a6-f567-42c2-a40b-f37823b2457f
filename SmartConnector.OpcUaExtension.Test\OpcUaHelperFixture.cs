using System;
using NUnit.Framework;
using Opc.Ua;
using SmartConnector.OpcUaExtension;
using Ews.Common;

namespace SmartConnector.OpcUaExtension.Test
{
    [TestFixture]
    public class OpcUaHelperFixture
    {
        [Test]
        public void IsValidNodeId_ValidNodeIds_ReturnsTrue()
        {
            // Arrange & Act & Assert
            Assert.IsTrue(OpcUaHelper.IsValidNodeId("ns=2;s=MyNode"));
            Assert.IsTrue(OpcUaHelper.IsValidNodeId("ns=1;i=1234"));
            Assert.IsTrue(OpcUaHelper.IsValidNodeId("i=85")); // Server object
            Assert.IsTrue(OpcUaHelper.IsValidNodeId("ns=0;i=2253")); // Server_ServerStatus
        }

        [Test]
        public void IsValidNodeId_InvalidNodeIds_ReturnsFalse()
        {
            // Arrange & Act & Assert
            Assert.IsFalse(OpcUaHelper.IsValidNodeId(""));
            Assert.IsFalse(OpcUaHelper.IsValidNodeId(null));
            Assert.IsFalse(OpcUaHelper.IsValidNodeId("invalid"));
            Assert.IsFalse(OpcUaHelper.IsValidNodeId("ns=;s="));
        }

        [Test]
        public void ConvertOpcUaValue_BooleanValue_ReturnsBoolean()
        {
            // Arrange
            var dataValue = new DataValue(true);

            // Act
            var result = OpcUaHelper.ConvertOpcUaValue(dataValue);

            // Assert
            Assert.IsInstanceOf<bool>(result);
            Assert.AreEqual(true, result);
        }

        [Test]
        public void ConvertOpcUaValue_IntegerValue_ReturnsInteger()
        {
            // Arrange
            var dataValue = new DataValue(42);

            // Act
            var result = OpcUaHelper.ConvertOpcUaValue(dataValue);

            // Assert
            Assert.IsInstanceOf<int>(result);
            Assert.AreEqual(42, result);
        }

        [Test]
        public void ConvertOpcUaValue_DoubleValue_ReturnsDouble()
        {
            // Arrange
            var dataValue = new DataValue(3.14);

            // Act
            var result = OpcUaHelper.ConvertOpcUaValue(dataValue);

            // Assert
            Assert.IsInstanceOf<double>(result);
            Assert.AreEqual(3.14, result);
        }

        [Test]
        public void ConvertOpcUaValue_StringValue_ReturnsString()
        {
            // Arrange
            var dataValue = new DataValue("Hello World");

            // Act
            var result = OpcUaHelper.ConvertOpcUaValue(dataValue);

            // Assert
            Assert.IsInstanceOf<string>(result);
            Assert.AreEqual("Hello World", result);
        }

        [Test]
        public void ConvertOpcUaValue_DateTimeValue_ReturnsDateTime()
        {
            // Arrange
            var testDate = new DateTime(2024, 1, 1, 12, 0, 0);
            var dataValue = new DataValue(testDate);

            // Act
            var result = OpcUaHelper.ConvertOpcUaValue(dataValue);

            // Assert
            Assert.IsInstanceOf<DateTime>(result);
            Assert.AreEqual(testDate, result);
        }

        [Test]
        public void ConvertOpcUaValue_NullValue_ReturnsNull()
        {
            // Arrange
            var dataValue = new DataValue(Variant.Null);

            // Act
            var result = OpcUaHelper.ConvertOpcUaValue(dataValue);

            // Assert
            Assert.IsNull(result);
        }

        [Test]
        public void GetEwsValueType_BooleanType_ReturnsBooleanEnum()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetEwsValueType(BuiltInType.Boolean);

            // Assert
            Assert.AreEqual(EwsValueTypeEnum.Boolean, result);
        }

        [Test]
        public void GetEwsValueType_IntegerTypes_ReturnsIntegerEnum()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(EwsValueTypeEnum.Integer, OpcUaHelper.GetEwsValueType(BuiltInType.Int16));
            Assert.AreEqual(EwsValueTypeEnum.Integer, OpcUaHelper.GetEwsValueType(BuiltInType.Int32));
            Assert.AreEqual(EwsValueTypeEnum.Integer, OpcUaHelper.GetEwsValueType(BuiltInType.UInt16));
        }

        [Test]
        public void GetEwsValueType_LongTypes_ReturnsLongEnum()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(EwsValueTypeEnum.Long, OpcUaHelper.GetEwsValueType(BuiltInType.Int64));
            Assert.AreEqual(EwsValueTypeEnum.Long, OpcUaHelper.GetEwsValueType(BuiltInType.UInt64));
            Assert.AreEqual(EwsValueTypeEnum.Long, OpcUaHelper.GetEwsValueType(BuiltInType.UInt32));
        }

        [Test]
        public void GetEwsValueType_FloatTypes_ReturnsDoubleEnum()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(EwsValueTypeEnum.Double, OpcUaHelper.GetEwsValueType(BuiltInType.Float));
            Assert.AreEqual(EwsValueTypeEnum.Double, OpcUaHelper.GetEwsValueType(BuiltInType.Double));
        }

        [Test]
        public void GetEwsValueType_StringTypes_ReturnsStringEnum()
        {
            // Arrange & Act & Assert
            Assert.AreEqual(EwsValueTypeEnum.String, OpcUaHelper.GetEwsValueType(BuiltInType.String));
            Assert.AreEqual(EwsValueTypeEnum.String, OpcUaHelper.GetEwsValueType(BuiltInType.NodeId));
            Assert.AreEqual(EwsValueTypeEnum.String, OpcUaHelper.GetEwsValueType(BuiltInType.QualifiedName));
        }

        [Test]
        public void GetEwsValueType_DateTimeType_ReturnsDateTimeEnum()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetEwsValueType(BuiltInType.DateTime);

            // Assert
            Assert.AreEqual(EwsValueTypeEnum.DateTime, result);
        }

        [Test]
        public void GetEwsValueState_GoodStatus_ReturnsGood()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetEwsValueState(StatusCodes.Good);

            // Assert
            Assert.AreEqual(EwsValueStateEnum.Good, result);
        }

        [Test]
        public void GetEwsValueState_UncertainStatus_ReturnsUncertain()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetEwsValueState(StatusCodes.UncertainInitialValue);

            // Assert
            Assert.AreEqual(EwsValueStateEnum.Uncertain, result);
        }

        [Test]
        public void GetEwsValueState_BadStatus_ReturnsBad()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetEwsValueState(StatusCodes.BadNodeIdUnknown);

            // Assert
            Assert.AreEqual(EwsValueStateEnum.Bad, result);
        }

        [Test]
        public void GetNodeMappingContainerId_ValidIndex_ReturnsFormattedId()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetNodeMappingContainerId(5);

            // Assert
            Assert.AreEqual("NodeMapping_5", result);
        }

        [Test]
        public void GetNodeMappingNodeIdId_ValidIndex_ReturnsFormattedId()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetNodeMappingNodeIdId(3);

            // Assert
            Assert.AreEqual("NodeMapping_3_NodeId", result);
        }

        [Test]
        public void GetNodeMappingValueId_ValidIndex_ReturnsFormattedId()
        {
            // Arrange & Act
            var result = OpcUaHelper.GetNodeMappingValueId(7);

            // Assert
            Assert.AreEqual("NodeMapping_7_Value", result);
        }
    }
}
