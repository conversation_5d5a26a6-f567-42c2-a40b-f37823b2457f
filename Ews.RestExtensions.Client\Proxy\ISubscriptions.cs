﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Subscriptions operations.
    /// </summary>
    public partial interface ISubscriptions
    {
        /// <summary>
        /// Creates a new Subscription and returns it.
        /// </summary>
        /// <param name='newItem'>
        /// Definition of how the Subscription will be created.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<SubscriptionModel>> CreateWithHttpMessagesAsync(NewSubscriptionModel newItem, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns all Subscription which fit the filter criteria supplied.
        /// All filter parameters are optional. Subscription will be ordered
        /// and paged as requested.
        /// </summary>
        /// <param name='type'>
        /// Return only Subscription with this Type value. Possible values
        /// include: 'ValueItemChanged', 'AlarmEventChanged',
        /// 'SystemEventChanged', 'HierarchyChanged'
        /// </param>
        /// <param name='orderBy'>
        /// Subscription will be returned in this order.  ExpiresAscending by
        /// default. Possible values include: 'ExpiresAscending',
        /// 'ExpiresDescending'
        /// </param>
        /// <param name='take'>
        /// Number of Subscription that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of Subscription that should be skipped before items are
        /// returned.  0 by default.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<SubscriptionModel>>> RetrieveWithHttpMessagesAsync(string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Subscription with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<SubscriptionModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the ExpiresOn property for the Subscription with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription whose ExpiresOn property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<DateTime?>> RetrieveExpiresOnWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Type property for the Subscription with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription whose Type property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveTypeWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the IsActive property for the Subscription with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription whose IsActive property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<bool?>> RetrieveIsActiveWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Renews the Subscription with the Id provided by the requested
        /// number of minutes.
        /// Returns the renewed Subscription.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription to be renewed.  Must be double URL encoded.
        /// </param>
        /// <param name='minutes'>
        /// Value in minutes to extend the Subscription.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<SubscriptionModel>> RenewWithHttpMessagesAsync(string id, int minutes, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Terminates the Subscription with the Id.  Terminated Subscription
        /// no longer can be used to create Notification sessions.
        /// Returns the terminated Subscription.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription to update.  Requires double URL encoding.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<SubscriptionModel>> TerminateWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Deletes the Subscription with the Id provided.  All Notification
        /// and NotificationItem created from the Subscription will also be
        /// deleted.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription to be deleted.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> DeleteWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Deletes Notification for the Subscription with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Subscription to act on.  Requires double URL encoding.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> DeleteNotificationsWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
