using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using Ews.Common;
using Mongoose.Common;
using Mongoose.Common.Attributes;
using Mongoose.Ews.Server.Data;
using Opc.Ua;
using Opc.Ua.Client;

namespace SmartConnector.OpcUaExtension
{
    [ConfigurationDefaults("OPC UA Update Processor", "Reads data from OPC UA server and updates EWS Server values.")]
    public class UpdateProcessor : OpcUaProcessorBase
    {
        #region UpdateIntervalSeconds
        [DefaultValue(5), <PERSON><PERSON><PERSON>("Interval in seconds between OPC UA data updates")]
        public int UpdateIntervalSeconds { get; set; } = 5;
        #endregion

        #region MaxNodeMappings
        [DefaultValue(50), Tooltip("Maximum number of node mappings to process")]
        public int MaxNodeMappings { get; set; } = 50;
        #endregion

        #region Execute_Subclass - Override
        protected override IEnumerable<Prompt> Execute_Subclass()
        {
            var prompts = new List<Prompt>();

            // Make sure we can connect to an EWS Server
            if (!IsEwsConnected) 
            {
                prompts.Add(CreateCannotConnectPrompt());
                return prompts;
            }

            // Try to connect to OPC UA server
            if (!IsOpcUaConnected)
            {
                prompts.Add(CreateCannotConnectOpcUaPrompt());
                UpdateConnectionStatus("Disconnected", DateTime.Now, "Failed to connect to OPC UA server");
                return prompts;
            }

            try
            {
                // Update connection status
                UpdateConnectionStatus("Connected", DateTime.Now, "");

                // Get the current endpoint URL from EWS (in case it was changed by user)
                UpdateOpcUaEndpointFromEws();

                // Read and update all configured node mappings
                UpdateNodeMappings();

                Logger.LogInfo(LogCategory.Processor, "Successfully updated OPC UA data");
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Processor, ex);
                UpdateConnectionStatus("Error", DateTime.Now, ex.Message);
                prompts.Add(new Prompt
                {
                    Message = $"Error updating OPC UA data: {ex.Message}",
                    Severity = PromptSeverity.MayNotContinue
                });
            }

            return prompts;
        }
        #endregion

        #region UpdateConnectionStatus
        private void UpdateConnectionStatus(string status, DateTime lastConnected, string errorMessage)
        {
            try
            {
                var statusItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.OpcUaConnectionStatusId);
                if (statusItem != null)
                {
                    DataAdapter.ModifyValueItemValue(statusItem, status, EwsValueStateEnum.Good);
                }

                var lastConnectedItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.OpcUaLastConnectedId);
                if (lastConnectedItem != null)
                {
                    DataAdapter.ModifyValueItemValue(lastConnectedItem, lastConnected, EwsValueStateEnum.Good);
                }

                var errorItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.OpcUaErrorMessageId);
                if (errorItem != null)
                {
                    DataAdapter.ModifyValueItemValue(errorItem, errorMessage ?? "", EwsValueStateEnum.Good);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Processor, ex);
            }
        }
        #endregion

        #region UpdateOpcUaEndpointFromEws
        private void UpdateOpcUaEndpointFromEws()
        {
            try
            {
                var endpointItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.OpcUaEndpointId);
                if (endpointItem != null && !string.IsNullOrEmpty(endpointItem.Value))
                {
                    var newEndpoint = endpointItem.Value;
                    if (newEndpoint != OpcUaEndpointUrl)
                    {
                        Logger.LogInfo(LogCategory.Processor, $"OPC UA endpoint changed from {OpcUaEndpointUrl} to {newEndpoint}");
                        OpcUaEndpointUrl = newEndpoint;
                        
                        // Force reconnection with new endpoint
                        if (_opcUaSession != null)
                        {
                            try
                            {
                                _opcUaSession.Close();
                                _opcUaSession.Dispose();
                            }
                            catch { }
                            _opcUaSession = null;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Processor, ex);
            }
        }
        #endregion

        #region UpdateNodeMappings
        private void UpdateNodeMappings()
        {
            var nodesToRead = new List<ReadValueId>();
            var mappingInfo = new List<(int index, string nodeId, EwsValueItem valueItem, EwsValueItem qualityItem, EwsValueItem timestampItem)>();

            // Collect all configured node mappings
            for (int i = 1; i <= MaxNodeMappings; i++)
            {
                try
                {
                    var nodeIdItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.GetNodeMappingNodeIdId(i));
                    if (nodeIdItem == null || string.IsNullOrEmpty(nodeIdItem.Value)) continue;

                    var nodeIdString = nodeIdItem.Value;
                    if (!OpcUaHelper.IsValidNodeId(nodeIdString)) continue;

                    var valueItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.GetNodeMappingValueId(i));
                    var qualityItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.GetNodeMappingQualityId(i));
                    var timestampItem = DataAdapter.ValueItems.FirstOrDefault(x => x.AlternateId == OpcUaHelper.GetNodeMappingTimestampId(i));

                    if (valueItem == null) continue;

                    var nodeId = NodeId.Parse(nodeIdString);
                    nodesToRead.Add(new ReadValueId
                    {
                        NodeId = nodeId,
                        AttributeId = Attributes.Value
                    });

                    mappingInfo.Add((i, nodeIdString, valueItem, qualityItem, timestampItem));
                }
                catch (Exception ex)
                {
                    Logger.LogError(LogCategory.Processor, $"Error preparing node mapping {i}: {ex.Message}");
                }
            }

            if (nodesToRead.Count == 0)
            {
                Logger.LogInfo(LogCategory.Processor, "No valid node mappings found to read");
                return;
            }

            // Read all nodes in a single call
            try
            {
                OpcUaSession.Read(null, 0, TimestampsToReturn.Both, nodesToRead, out var results, out var diagnosticInfos);

                // Update EWS values with results
                for (int i = 0; i < results.Count && i < mappingInfo.Count; i++)
                {
                    var result = results[i];
                    var mapping = mappingInfo[i];

                    try
                    {
                        UpdateMappingValue(mapping.index, mapping.valueItem, mapping.qualityItem, mapping.timestampItem, result);
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError(LogCategory.Processor, $"Error updating mapping {mapping.index}: {ex.Message}");
                    }
                }

                Logger.LogInfo(LogCategory.Processor, $"Successfully read {results.Count} OPC UA nodes");
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Processor, $"Error reading OPC UA nodes: {ex.Message}");
                throw;
            }
        }
        #endregion

        #region UpdateMappingValue
        private void UpdateMappingValue(int mappingIndex, EwsValueItem valueItem, EwsValueItem qualityItem, EwsValueItem timestampItem, DataValue dataValue)
        {
            try
            {
                // Convert and update the value
                var convertedValue = OpcUaHelper.ConvertOpcUaValue(dataValue);
                var ewsState = OpcUaHelper.GetEwsValueState(dataValue.StatusCode);
                
                if (convertedValue != null)
                {
                    DataAdapter.ModifyValueItemValue(valueItem, convertedValue, ewsState);
                }
                else
                {
                    DataAdapter.ModifyValueItemValue(valueItem, "", EwsValueStateEnum.Bad);
                }

                // Update quality if item exists
                if (qualityItem != null)
                {
                    DataAdapter.ModifyValueItemValue(qualityItem, dataValue.StatusCode.ToString(), EwsValueStateEnum.Good);
                }

                // Update timestamp if item exists
                if (timestampItem != null && dataValue.SourceTimestamp != DateTime.MinValue)
                {
                    DataAdapter.ModifyValueItemValue(timestampItem, dataValue.SourceTimestamp, EwsValueStateEnum.Good);
                }
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Processor, $"Error updating mapping {mappingIndex} value: {ex.Message}");
                
                // Set error state
                try
                {
                    DataAdapter.ModifyValueItemValue(valueItem, $"Error: {ex.Message}", EwsValueStateEnum.Bad);
                }
                catch { }
            }
        }
        #endregion

        #region Private Fields
        private Session _opcUaSession = null;
        #endregion
    }
}
