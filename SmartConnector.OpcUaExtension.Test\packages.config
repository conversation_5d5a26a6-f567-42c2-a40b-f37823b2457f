<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net452" />
  <package id="Ews.Client" version="2.4.10" targetFramework="net452" />
  <package id="Ews.Common" version="2.4.10" targetFramework="net452" />
  <package id="Ews.Server.Contract" version="2.4.10" targetFramework="net452" />
  <package id="Mongoose.Common" version="2.4.10" targetFramework="net452" />
  <package id="Mongoose.Ews.Server.Data" version="2.4.10" targetFramework="net452" />
  <package id="Mongoose.Process" version="2.4.10" targetFramework="net452" />
  <package id="NLog" version="4.4.12" targetFramework="net452" />
  <package id="NUnit" version="3.12.0" targetFramework="net452" />
  <package id="OPCFoundation.NetStandard.Opc.Ua" version="1.4.371.86" targetFramework="net462" />
  <package id="OPCFoundation.NetStandard.Opc.Ua.Client" version="1.4.371.86" targetFramework="net462" />
  <package id="SxL.Common" version="2.4.10" targetFramework="net452" />
</packages>
