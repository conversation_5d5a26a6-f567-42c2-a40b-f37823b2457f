﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="EntityFramework" version="6.2.0" targetFramework="net452" />
  <package id="Ews.Client" version="2.4.10" targetFramework="net452" />
  <package id="Ews.Common" version="2.4.10" targetFramework="net452" />
  <package id="Ews.Server.Contract" version="2.4.10" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Core" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.Identity.Owin" version="2.2.1" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Client" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Core" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.Owin" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.OwinSelfHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.AspNet.WebApi.WebHost" version="5.2.3" targetFramework="net452" />
  <package id="Microsoft.Owin" version="3.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Host.HttpListener" version="3.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Hosting" version="3.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security" version="3.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.Cookies" version="3.1.0" targetFramework="net452" />
  <package id="Microsoft.Owin.Security.OAuth" version="3.1.0" targetFramework="net452" />
  <package id="Mongoose.Common" version="2.4.10" targetFramework="net452" />
  <package id="Mongoose.Ews.Server" version="2.4.10" targetFramework="net452" />
  <package id="Mongoose.Ews.Server.Data" version="2.4.10" targetFramework="net452" />
  <package id="Mongoose.Process" version="2.4.10" targetFramework="net452" />
  <package id="Newtonsoft.Json" version="10.0.3" targetFramework="net452" />
  <package id="NLog" version="4.4.12" targetFramework="net452" />
  <package id="Owin" version="1.0" targetFramework="net452" />
  <package id="SmartConnector.Utilities" version="*******" targetFramework="net462" />
  <package id="Swashbuckle.Core" version="5.6.0" targetFramework="net452" />
  <package id="SxL.Common" version="2.4.10" targetFramework="net452" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net462" />
  <package id="WebApiThrottle.StrongName" version="1.5.4" targetFramework="net452" />
</packages>