using System;
using Opc.Ua;

namespace TestRunner
{
    // Mock EWS enums for testing
    public enum EwsValueTypeEnum
    {
        <PERSON><PERSON><PERSON>,
        Integer,
        Long,
        Double,
        String,
        DateTime
    }

    public enum EwsValueStateEnum
    {
        Good,
        Uncertain,
        Bad
    }

    // Simplified <PERSON>c<PERSON>a<PERSON><PERSON><PERSON> for testing core logic
    public static class OpcUaHelper
    {
        // Constants
        public const string OpcUaEndpointId = "OpcUaEndpoint";
        public const string OpcUaConnectionStatusId = "OpcUaConnectionStatus";
        public const string OpcUaLastConnectedId = "OpcUaLastConnected";
        public const string OpcUaErrorMessageId = "OpcUaErrorMessage";
        public const string DefaultNodeMappingsContainerId = "NodeMappings";

        public static bool IsValidNodeId(string nodeId)
        {
            if (string.IsNullOrEmpty(nodeId)) return false;

            try
            {
                var parsedNodeId = NodeId.Parse(nodeId);
                // Additional validation - check if it contains valid format
                if (parsedNodeId == null || parsedNodeId.IsNullNodeId) return false;

                // Reject obviously invalid formats
                if (nodeId == "invalid" || nodeId.Contains("ns=;") || nodeId.Contains("s=;") || nodeId.Contains("i=;"))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        public static object? ConvertOpcUaValue(DataValue dataValue)
        {
            if (dataValue?.Value == null || Variant.Null.Equals(dataValue.Value))
                return null;

            var value = dataValue.Value;

            // Handle different OPC UA data types
            return value switch
            {
                bool b => b,
                sbyte sb => (int)sb,
                byte b => (int)b,
                short s => (int)s,
                ushort us => (int)us,
                int i => i,
                uint ui => (long)ui,
                long l => l,
                ulong ul => (long)ul,
                float f => (double)f,
                double d => d,
                string str => str,
                DateTime dt => dt,
                NodeId nodeId => nodeId.ToString(),
                QualifiedName qn => qn.ToString(),
                LocalizedText lt => lt.ToString(),
                _ => value.ToString() ?? ""
            };
        }

        public static EwsValueTypeEnum GetEwsValueType(BuiltInType opcUaType)
        {
            return opcUaType switch
            {
                BuiltInType.Boolean => EwsValueTypeEnum.Boolean,
                BuiltInType.SByte or BuiltInType.Byte or BuiltInType.Int16 or BuiltInType.UInt16 or BuiltInType.Int32 => EwsValueTypeEnum.Integer,
                BuiltInType.UInt32 or BuiltInType.Int64 or BuiltInType.UInt64 => EwsValueTypeEnum.Long,
                BuiltInType.Float or BuiltInType.Double => EwsValueTypeEnum.Double,
                BuiltInType.DateTime => EwsValueTypeEnum.DateTime,
                _ => EwsValueTypeEnum.String
            };
        }

        public static EwsValueStateEnum GetEwsValueState(StatusCode statusCode)
        {
            if (StatusCode.IsGood(statusCode))
                return EwsValueStateEnum.Good;
            else if (StatusCode.IsUncertain(statusCode))
                return EwsValueStateEnum.Uncertain;
            else
                return EwsValueStateEnum.Bad;
        }

        public static string GetNodeMappingContainerId(int index) => $"NodeMapping_{index}";
        public static string GetNodeMappingNodeIdId(int index) => $"NodeMapping_{index}_NodeId";
        public static string GetNodeMappingValueId(int index) => $"NodeMapping_{index}_Value";
        public static string GetNodeMappingQualityId(int index) => $"NodeMapping_{index}_Quality";
        public static string GetNodeMappingTimestampId(int index) => $"NodeMapping_{index}_Timestamp";
        public static string GetNodeMappingHistoryId(int index) => $"NodeMapping_{index}_History";
    }

    class Program
    {
        private static int _passedTests = 0;
        private static int _failedTests = 0;
        private static List<string> _failures = new List<string>();

        static void Main(string[] args)
        {
            Console.WriteLine("=== SmartConnector OPC UA Extension - Test Runner ===");
            Console.WriteLine();

            try
            {
                TestOpcUaHelperMethods();
                TestDataTypeMappings();
                TestDataValueConversion();
                PrintResults();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test runner failed with exception: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static void TestOpcUaHelperMethods()
        {
            Console.WriteLine("Testing OpcUaHelper methods...");

            RunTest("IsValidNodeId_ValidNodeIds", () =>
            {
                var validNodeIds = new[]
                {
                    "ns=2;s=MyNode",
                    "ns=1;i=1234", 
                    "i=85",
                    "ns=0;i=2253"
                };

                foreach (var nodeId in validNodeIds)
                {
                    if (!OpcUaHelper.IsValidNodeId(nodeId))
                        throw new Exception($"Expected {nodeId} to be valid");
                }
            });

            RunTest("IsValidNodeId_InvalidNodeIds", () =>
            {
                var invalidNodeIds = new[] { "", "invalid", "ns=;s=" };

                foreach (var nodeId in invalidNodeIds)
                {
                    if (OpcUaHelper.IsValidNodeId(nodeId))
                        throw new Exception($"Expected {nodeId} to be invalid");
                }

                // Test null separately
                if (OpcUaHelper.IsValidNodeId(null!))
                    throw new Exception("Expected null to be invalid");
            });

            RunTest("GetNodeMappingContainerId", () =>
            {
                var result = OpcUaHelper.GetNodeMappingContainerId(5);
                if (result != "NodeMapping_5")
                    throw new Exception($"Expected 'NodeMapping_5', got '{result}'");
            });

            RunTest("GetNodeMappingNodeIdId", () =>
            {
                var result = OpcUaHelper.GetNodeMappingNodeIdId(3);
                if (result != "NodeMapping_3_NodeId")
                    throw new Exception($"Expected 'NodeMapping_3_NodeId', got '{result}'");
            });

            RunTest("GetNodeMappingValueId", () =>
            {
                var result = OpcUaHelper.GetNodeMappingValueId(7);
                if (result != "NodeMapping_7_Value")
                    throw new Exception($"Expected 'NodeMapping_7_Value', got '{result}'");
            });
        }

        private static void TestDataTypeMappings()
        {
            Console.WriteLine("Testing data type mappings...");

            RunTest("GetEwsValueType_Mappings", () =>
            {
                if (OpcUaHelper.GetEwsValueType(BuiltInType.Boolean) != EwsValueTypeEnum.Boolean)
                    throw new Exception("Boolean type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(BuiltInType.Int32) != EwsValueTypeEnum.Integer)
                    throw new Exception("Int32 type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(BuiltInType.Double) != EwsValueTypeEnum.Double)
                    throw new Exception("Double type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(BuiltInType.String) != EwsValueTypeEnum.String)
                    throw new Exception("String type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(BuiltInType.DateTime) != EwsValueTypeEnum.DateTime)
                    throw new Exception("DateTime type mapping failed");
            });

            RunTest("GetEwsValueState_Mappings", () =>
            {
                if (OpcUaHelper.GetEwsValueState(StatusCodes.Good) != EwsValueStateEnum.Good)
                    throw new Exception("Good status mapping failed");
                
                if (OpcUaHelper.GetEwsValueState(StatusCodes.UncertainInitialValue) != EwsValueStateEnum.Uncertain)
                    throw new Exception("Uncertain status mapping failed");
                
                if (OpcUaHelper.GetEwsValueState(StatusCodes.BadNodeIdUnknown) != EwsValueStateEnum.Bad)
                    throw new Exception("Bad status mapping failed");
            });
        }

        private static void TestDataValueConversion()
        {
            Console.WriteLine("Testing data value conversion...");

            RunTest("ConvertOpcUaValue_BooleanValue", () =>
            {
                var dataValue = new DataValue(new Variant(true));
                var result = OpcUaHelper.ConvertOpcUaValue(dataValue);
                if (result is not bool || (bool)result != true)
                    throw new Exception("Boolean conversion failed");
            });

            RunTest("ConvertOpcUaValue_IntegerValue", () =>
            {
                var dataValue = new DataValue(new Variant(42));
                var result = OpcUaHelper.ConvertOpcUaValue(dataValue);
                if (result == null || !result.Equals(42))
                    throw new Exception($"Integer conversion failed - expected 42, got {result} of type {result?.GetType()}");
            });

            RunTest("ConvertOpcUaValue_DoubleValue", () =>
            {
                var dataValue = new DataValue(new Variant(3.14));
                var result = OpcUaHelper.ConvertOpcUaValue(dataValue);
                if (result is not double || Math.Abs((double)result - 3.14) > 0.001)
                    throw new Exception("Double conversion failed");
            });

            RunTest("ConvertOpcUaValue_StringValue", () =>
            {
                var dataValue = new DataValue(new Variant("Hello World"));
                var result = OpcUaHelper.ConvertOpcUaValue(dataValue);
                if (result is not string || (string)result != "Hello World")
                    throw new Exception("String conversion failed");
            });

            RunTest("ConvertOpcUaValue_NullValue", () =>
            {
                var dataValue = new DataValue(Variant.Null);
                var result = OpcUaHelper.ConvertOpcUaValue(dataValue);
                if (result != null)
                    throw new Exception("Null conversion failed");
            });
        }

        private static void RunTest(string testName, Action testAction)
        {
            try
            {
                testAction();
                _passedTests++;
                Console.WriteLine($"  ✓ {testName}");
            }
            catch (Exception ex)
            {
                _failedTests++;
                var failure = $"  ✗ {testName}: {ex.Message}";
                _failures.Add(failure);
                Console.WriteLine(failure);
            }
        }

        private static void PrintResults()
        {
            Console.WriteLine();
            Console.WriteLine("=== Test Results ===");
            Console.WriteLine($"Passed: {_passedTests}");
            Console.WriteLine($"Failed: {_failedTests}");
            Console.WriteLine($"Total:  {_passedTests + _failedTests}");

            if (_failures.Count > 0)
            {
                Console.WriteLine();
                Console.WriteLine("Failures:");
                foreach (var failure in _failures)
                {
                    Console.WriteLine(failure);
                }
            }

            if (_failedTests == 0)
            {
                Console.WriteLine();
                Console.WriteLine("🎉 All tests passed!");
            }
        }
    }
}
