﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A Subscription is as data registration to one or more items for the
    /// purpose of creating Notification sessions to retrieve
    /// NotificationItem when the subscribed items have changed.
    /// </summary>
    public partial class SubscriptionModel
    {
        /// <summary>
        /// Initializes a new instance of the SubscriptionModel class.
        /// </summary>
        public SubscriptionModel() { }

        /// <summary>
        /// Initializes a new instance of the SubscriptionModel class.
        /// </summary>
        public SubscriptionModel(string type = default(string), bool? isActive = default(bool?), DateTime? expiresOn = default(DateTime?), string id = default(string))
        {
            Type = type;
            IsActive = isActive;
            ExpiresOn = expiresOn;
            Id = id;
        }

        /// <summary>
        /// Defines the type of items which are being subscribed to. Possible
        /// values include: 'ValueItemChanged', 'AlarmEventChanged',
        /// 'SystemEventChanged', 'HierarchyChanged'
        /// </summary>
        [JsonProperty(PropertyName = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Indicates if the Subscription is active and new Notification can
        /// be generated.
        /// </summary>
        [JsonProperty(PropertyName = "IsActive")]
        public bool? IsActive { get; set; }

        /// <summary>
        /// DateTime in UTC when the Subscription will expire.
        /// </summary>
        [JsonProperty(PropertyName = "ExpiresOn")]
        public DateTime? ExpiresOn { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
