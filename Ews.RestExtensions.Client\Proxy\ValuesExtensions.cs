﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for Values.
    /// </summary>
    public static partial class ValuesExtensions
    {
            /// <summary>
            /// Creates a new Value and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Value will be created.
            /// </param>
            public static ValueModel Create(this IValues operations, NewValueModel newItem)
            {
                return Task.Factory.StartNew(s => ((IValues)s).CreateAsync(newItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Creates a new Value and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Value will be created.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> CreateAsync(this IValues operations, NewValueModel newItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(newItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all Value which fit the filter criteria supplied. All filter
            /// parameters are optional. Value will be ordered and paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only Value whose name contains this value.
            /// </param>
            /// <param name='type'>
            /// Return only Value with this Type value. Possible values include:
            /// 'DateTime', 'Boolean', 'String', 'Double', 'Long', 'Integer', 'Duration'
            /// </param>
            /// <param name='orderBy'>
            /// Value will be returned in this order.  NameAscending by default. Possible
            /// values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Value that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Value that should be skipped before items are returned.  0 by
            /// default.
            /// </param>
            public static IList<ValueModel> Retrieve(this IValues operations, string name = default(string), string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveAsync(name, type, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all Value which fit the filter criteria supplied. All filter
            /// parameters are optional. Value will be ordered and paged as requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='name'>
            /// Return only Value whose name contains this value.
            /// </param>
            /// <param name='type'>
            /// Return only Value with this Type value. Possible values include:
            /// 'DateTime', 'Boolean', 'String', 'Double', 'Long', 'Integer', 'Duration'
            /// </param>
            /// <param name='orderBy'>
            /// Value will be returned in this order.  NameAscending by default. Possible
            /// values include: 'NameAscending', 'NameDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Value that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Value that should be skipped before items are returned.  0 by
            /// default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<ValueModel>> RetrieveAsync(this IValues operations, string name = default(string), string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(name, type, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value requested.  Must be double URL encoded.
            /// </param>
            public static ValueModel RetrieveById(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> RetrieveByIdAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Description property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Description property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static object RetrieveDescription(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveDescriptionAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Description property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Description property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> RetrieveDescriptionAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveDescriptionWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Description property for the Value with the Id provided to
            /// the new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Description property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            public static ValueModel UpdateDescription(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateDescriptionAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Description property for the Value with the Id provided to
            /// the new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Description property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Description value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateDescriptionAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateDescriptionWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Forceable property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Forceable property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveForceable(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveForceableAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Forceable property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Forceable property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveForceableAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveForceableWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Forceable property for the Value with the Id provided to the
            /// new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Forceable property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Forceable value.
            /// </param>
            public static ValueModel UpdateForceable(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateForceableAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Forceable property for the Value with the Id provided to the
            /// new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Forceable property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Forceable value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateForceableAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateForceableWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Name property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Name property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            public static string RetrieveName(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveNameAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Name property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Name property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveNameAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveNameWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Name property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Name property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value.
            /// </param>
            public static ValueModel UpdateName(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateNameAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Name property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Name property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Name value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateNameAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateNameWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the parent Container for the Value with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose parent Container is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static ContainerModel RetrieveParent(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveParentAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the parent Container for the Value with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose parent Container is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ContainerModel> RetrieveParentAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveParentWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the ParentId property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose ParentId property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveParentId(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveParentIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the ParentId property for the Container with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose ParentId property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveParentIdAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveParentIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the ParentId property for the Value with the Id provided to the
            /// new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose ParentId property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New ParentId value.
            /// </param>
            public static ValueModel UpdateParentId(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateParentIdAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the ParentId property for the Value with the Id provided to the
            /// new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose ParentId property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New ParentId value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateParentIdAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateParentIdWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the State property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the State whose Type property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            public static string RetrieveState(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveStateAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the State property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the State whose Type property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveStateAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveStateWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the State property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the State whose Type property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New State value.
            /// </param>
            public static ValueModel UpdateState(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateStateAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the State property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the State whose Type property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New State value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateStateAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateStateWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Type property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Type property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            public static string RetrieveType(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveTypeAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Type property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Type property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveTypeAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTypeWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Type property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Type property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Type value.
            /// </param>
            public static ValueModel UpdateType(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateTypeAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Type property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Type property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Type value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateTypeAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateTypeWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Unit property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Unit property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            public static string RetrieveUnit(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveUnitAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Unit property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Unit property is to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveUnitAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveUnitWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Unit property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Unit property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Unit value.
            /// </param>
            public static ValueModel UpdateUnit(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateUnitAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Unit property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Unit property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Unit value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateUnitAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateUnitWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Value property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Value property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            public static string RetrieveValue(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveValueAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Value property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Value property is to be returned.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveValueAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveValueWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Value property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Value property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Value value.
            /// </param>
            public static ValueModel UpdateValue(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateValueAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Value property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Value property is to be modified.  Must be double
            /// URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Value value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateValueAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateValueWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Writeable property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Writeable property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveWriteable(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).RetrieveWriteableAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Writeable property for the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Writeable property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveWriteableAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWriteableWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Writeable property for the Value with the Id provided to the
            /// new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Writeable property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Writeable value.
            /// </param>
            public static ValueModel UpdateWriteable(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateWriteableAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Writeable property for the Value with the Id provided to the
            /// new value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Writeable property is to be modified.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Writeable value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateWriteableAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateWriteableWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Value with the Id provided to the values supplied.  Returns
            /// the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='updatedItem'>
            /// Values to update the Value with.
            /// </param>
            public static ValueModel Update(this IValues operations, string id, UpdateValueModel updatedItem)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateAsync(id, updatedItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Value with the Id provided to the values supplied.  Returns
            /// the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to be modified.  Must be double URL encoded.
            /// </param>
            /// <param name='updatedItem'>
            /// Values to update the Value with.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateAsync(this IValues operations, string id, UpdateValueModel updatedItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateWithHttpMessagesAsync(id, updatedItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Modifies the Id property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Id property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Id value.
            /// </param>
            public static ValueModel UpdateId(this IValues operations, string id, string newValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UpdateIdAsync(id, newValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Modifies the Id property for the Value with the Id provided to the new
            /// value supplied.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value whose Id property is to be modified.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='newValue'>
            /// New Id value.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UpdateIdAsync(this IValues operations, string id, string newValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UpdateIdWithHttpMessagesAsync(id, newValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Forces the Value with the Id provided.
            /// After this action the Value will have a State of Forced and a Value equal
            /// to the forcedValue parameter.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to be forced.  Must be double URL encoded.
            /// </param>
            /// <param name='forcedValue'>
            /// Value which the Value will be forced to.
            /// </param>
            public static ValueModel Force(this IValues operations, string id, string forcedValue)
            {
                return Task.Factory.StartNew(s => ((IValues)s).ForceAsync(id, forcedValue), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Forces the Value with the Id provided.
            /// After this action the Value will have a State of Forced and a Value equal
            /// to the forcedValue parameter.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to be forced.  Must be double URL encoded.
            /// </param>
            /// <param name='forcedValue'>
            /// Value which the Value will be forced to.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> ForceAsync(this IValues operations, string id, string forcedValue, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.ForceWithHttpMessagesAsync(id, forcedValue, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Unforces the Value with the Id provided.
            /// After this action the Value will have a State and Value which depends on
            /// configured Data Provider.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to update.  Requires double URL encoding.
            /// </param>
            public static ValueModel Unforce(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).UnforceAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Unforces the Value with the Id provided.
            /// After this action the Value will have a State and Value which depends on
            /// configured Data Provider.  Returns the modified Value.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to update.  Requires double URL encoding.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<ValueModel> UnforceAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.UnforceWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to be deleted.  Must be double URL encoded.
            /// </param>
            public static object Delete(this IValues operations, string id)
            {
                return Task.Factory.StartNew(s => ((IValues)s).DeleteAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes the Value with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Value to be deleted.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteAsync(this IValues operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
