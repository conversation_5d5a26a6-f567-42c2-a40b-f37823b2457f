﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A Notification represents a logical session of NotificationItem for a
    /// Subscription at point in time.
    /// </summary>
    public partial class NotificationModel
    {
        /// <summary>
        /// Initializes a new instance of the NotificationModel class.
        /// </summary>
        public NotificationModel() { }

        /// <summary>
        /// Initializes a new instance of the NotificationModel class.
        /// </summary>
        public NotificationModel(string subscriptionId = default(string), DateTime? createdOn = default(DateTime?), string id = default(string))
        {
            SubscriptionId = subscriptionId;
            CreatedOn = createdOn;
            Id = id;
        }

        /// <summary>
        /// The Subscription that this Notification is for.
        /// </summary>
        [JsonProperty(PropertyName = "SubscriptionId")]
        public string SubscriptionId { get; set; }

        /// <summary>
        /// DateTime in UTC when the Notification session was created.
        /// </summary>
        [JsonProperty(PropertyName = "CreatedOn")]
        public DateTime? CreatedOn { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
