﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// Represents a Subscription creation request.
    /// </summary>
    public partial class NewSubscriptionModel
    {
        /// <summary>
        /// Initializes a new instance of the NewSubscriptionModel class.
        /// </summary>
        public NewSubscriptionModel() { }

        /// <summary>
        /// Initializes a new instance of the NewSubscriptionModel class.
        /// </summary>
        public NewSubscriptionModel(int? durationInMinutes = default(int?), string subscriptionType = default(string), IList<string> ids = default(IList<string>), int? priorityFrom = default(int?), int? priorityTo = default(int?), IList<string> types = default(IList<string>))
        {
            DurationInMinutes = durationInMinutes;
            SubscriptionType = subscriptionType;
            Ids = ids;
            PriorityFrom = priorityFrom;
            PriorityTo = priorityTo;
            Types = types;
        }

        /// <summary>
        /// The requested  duration of the Subscription.
        /// </summary>
        [JsonProperty(PropertyName = "DurationInMinutes")]
        public int? DurationInMinutes { get; set; }

        /// <summary>
        /// The type of data to Subscribe to. Possible values include:
        /// 'ValueItemChanged', 'AlarmEventChanged', 'SystemEventChanged',
        /// 'HierarchyChanged'
        /// </summary>
        [JsonProperty(PropertyName = "SubscriptionType")]
        public string SubscriptionType { get; set; }

        /// <summary>
        /// List of Ids of items which are being Subscribed to.
        /// </summary>
        [JsonProperty(PropertyName = "Ids")]
        public IList<string> Ids { get; set; }

        /// <summary>
        /// For AlarmEventChanged and SystemEventChanged subscriptions, only
        /// items with Priority at or above this value will be included.
        /// </summary>
        [JsonProperty(PropertyName = "PriorityFrom")]
        public int? PriorityFrom { get; set; }

        /// <summary>
        /// For AlarmEventChanged and SystemEventChanged subscriptions, only
        /// items with Priority below this value will be included.
        /// </summary>
        [JsonProperty(PropertyName = "PriorityTo")]
        public int? PriorityTo { get; set; }

        /// <summary>
        /// For AlarmEventChanged and SystemEventChanged subscriptions, only
        /// items with a Type in this List will be included.
        /// </summary>
        [JsonProperty(PropertyName = "Types")]
        public IList<string> Types { get; set; }

    }
}
