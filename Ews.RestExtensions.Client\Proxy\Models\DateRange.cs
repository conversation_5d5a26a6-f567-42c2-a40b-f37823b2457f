﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A simple model to represent open ended range of dates.
    /// </summary>
    public partial class DateRange
    {
        /// <summary>
        /// Initializes a new instance of the DateRange class.
        /// </summary>
        public DateRange() { }

        /// <summary>
        /// Initializes a new instance of the DateRange class.
        /// </summary>
        public DateRange(DateTime? onOrAfter = default(DateTime?), DateTime? before = default(DateTime?))
        {
            OnOrAfter = onOrAfter;
            Before = before;
        }

        /// <summary>
        /// Lower limit of the range.  Range wil include all dates on or after
        /// this value.
        /// </summary>
        [JsonProperty(PropertyName = "OnOrAfter")]
        public DateTime? OnOrAfter { get; set; }

        /// <summary>
        /// Uppwer limit of the range.  Range will include all dates before
        /// this value.
        /// </summary>
        [JsonProperty(PropertyName = "Before")]
        public DateTime? Before { get; set; }

    }
}
