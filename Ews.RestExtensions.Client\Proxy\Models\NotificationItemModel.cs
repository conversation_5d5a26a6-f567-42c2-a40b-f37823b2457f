﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A NotificationItem represents a single event notification for given
    /// Notification session belonging to a Subscription.
    /// </summary>
    public partial class NotificationItemModel
    {
        /// <summary>
        /// Initializes a new instance of the NotificationItemModel class.
        /// </summary>
        public NotificationItemModel() { }

        /// <summary>
        /// Initializes a new instance of the NotificationItemModel class.
        /// </summary>
        public NotificationItemModel(string subscriptionId = default(string), string notificationId = default(string), string changeType = default(string), string changedItemId = default(string), string changedItemType = default(string), DateTime? changedAt = default(DateTime?), string value = default(string), string valueState = default(string), string alarmEventState = default(string), string id = default(string))
        {
            SubscriptionId = subscriptionId;
            NotificationId = notificationId;
            ChangeType = changeType;
            ChangedItemId = changedItemId;
            ChangedItemType = changedItemType;
            ChangedAt = changedAt;
            Value = value;
            ValueState = valueState;
            AlarmEventState = alarmEventState;
            Id = id;
        }

        /// <summary>
        /// The Subscription that this NotificationItem is for.
        /// </summary>
        [JsonProperty(PropertyName = "SubscriptionId")]
        public string SubscriptionId { get; set; }

        /// <summary>
        /// The Notification that this NotificationItem is for.
        /// </summary>
        [JsonProperty(PropertyName = "NotificationId")]
        public string NotificationId { get; set; }

        /// <summary>
        /// The type of change this is a NotificationItem for. Possible values
        /// include: 'ValueItemChanged', 'AlarmEventChanged',
        /// 'SystemEventChanged', 'HierarchyChanged'
        /// </summary>
        [JsonProperty(PropertyName = "ChangeType")]
        public string ChangeType { get; set; }

        /// <summary>
        /// Id of the item that changed.
        /// </summary>
        [JsonProperty(PropertyName = "ChangedItemId")]
        public string ChangedItemId { get; set; }

        /// <summary>
        /// The type of entity which ChangedItemId refers to. Possible values
        /// include: 'ContainerItem', 'ValueItem', 'HistoryItem',
        /// 'AlarmItem', 'EnumItem'
        /// </summary>
        [JsonProperty(PropertyName = "ChangedItemType")]
        public string ChangedItemType { get; set; }

        /// <summary>
        /// The DateTime in UTC when the change occurred.  Not available for
        /// all ChangeType.
        /// </summary>
        [JsonProperty(PropertyName = "ChangedAt")]
        public DateTime? ChangedAt { get; set; }

        /// <summary>
        /// The Value at the time of the change.  (ChangeType =
        /// ValueItemChanged only).
        /// </summary>
        [JsonProperty(PropertyName = "Value")]
        public string Value { get; set; }

        /// <summary>
        /// The State at the time of the change.  (ChangeType =
        /// ValueItemChanged only). Possible values include: 'Good',
        /// 'Uncertain', 'Forced', 'Offline', 'Error'
        /// </summary>
        [JsonProperty(PropertyName = "ValueState")]
        public string ValueState { get; set; }

        /// <summary>
        /// The Alarm State at the time of the change.  (ChangeType =
        /// AlarmEventChanged only). Possible values include: 'Normal',
        /// 'Active', 'Acknowledged', 'Reset', 'Disabled'
        /// </summary>
        [JsonProperty(PropertyName = "AlarmEventState")]
        public string AlarmEventState { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
