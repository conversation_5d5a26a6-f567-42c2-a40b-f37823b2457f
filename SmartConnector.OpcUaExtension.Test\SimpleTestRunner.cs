using System;
using System.Collections.Generic;
using System.Reflection;

namespace SmartConnector.OpcUaExtension.Test
{
    /// <summary>
    /// Simple test runner for basic validation without requiring full build environment
    /// </summary>
    public class SimpleTestRunner
    {
        private static int _passedTests = 0;
        private static int _failedTests = 0;
        private static List<string> _failures = new List<string>();

        public static void Main(string[] args)
        {
            Console.WriteLine("=== SmartConnector OPC UA Extension - Simple Test Runner ===");
            Console.WriteLine();

            try
            {
                // Test OpcUaHelper static methods
                TestOpcUaHelperMethods();

                // Test processor configuration validation
                TestProcessorConfiguration();

                // Test data type mappings
                TestDataTypeMappings();

                // Print results
                PrintResults();
            }
            catch (Exception ex)
            {
                Console.WriteLine($"Test runner failed with exception: {ex.Message}");
                Console.WriteLine(ex.StackTrace);
            }

            Console.WriteLine();
            Console.WriteLine("Press any key to exit...");
            Console.ReadKey();
        }

        private static void TestOpcUaHelperMethods()
        {
            Console.WriteLine("Testing OpcUaHelper methods...");

            // Test IsValidNodeId method
            RunTest("IsValidNodeId_ValidNodeIds", () =>
            {
                var validNodeIds = new[]
                {
                    "ns=2;s=MyNode",
                    "ns=1;i=1234", 
                    "i=85",
                    "ns=0;i=2253"
                };

                foreach (var nodeId in validNodeIds)
                {
                    if (!OpcUaHelper.IsValidNodeId(nodeId))
                        throw new Exception($"Expected {nodeId} to be valid");
                }
            });

            RunTest("IsValidNodeId_InvalidNodeIds", () =>
            {
                var invalidNodeIds = new[] { "", null, "invalid", "ns=;s=" };

                foreach (var nodeId in invalidNodeIds)
                {
                    if (OpcUaHelper.IsValidNodeId(nodeId))
                        throw new Exception($"Expected {nodeId} to be invalid");
                }
            });

            // Test ID generation methods
            RunTest("GetNodeMappingContainerId", () =>
            {
                var result = OpcUaHelper.GetNodeMappingContainerId(5);
                if (result != "NodeMapping_5")
                    throw new Exception($"Expected 'NodeMapping_5', got '{result}'");
            });

            RunTest("GetNodeMappingNodeIdId", () =>
            {
                var result = OpcUaHelper.GetNodeMappingNodeIdId(3);
                if (result != "NodeMapping_3_NodeId")
                    throw new Exception($"Expected 'NodeMapping_3_NodeId', got '{result}'");
            });

            RunTest("GetNodeMappingValueId", () =>
            {
                var result = OpcUaHelper.GetNodeMappingValueId(7);
                if (result != "NodeMapping_7_Value")
                    throw new Exception($"Expected 'NodeMapping_7_Value', got '{result}'");
            });
        }

        private static void TestProcessorConfiguration()
        {
            Console.WriteLine("Testing processor configuration...");

            RunTest("SetupProcessor_DefaultValues", () =>
            {
                using (var processor = new SetupProcessor())
                {
                    if (processor.ServerName != "SmartConnector OPC UA Service")
                        throw new Exception("Unexpected ServerName default value");
                    
                    if (processor.UserName != "admin")
                        throw new Exception("Unexpected UserName default value");
                    
                    if (processor.Password != "Admin!23")
                        throw new Exception("Unexpected Password default value");
                    
                    if (processor.EwsAddress != "http://localhost:5400/SmartConnectorOpcUaService")
                        throw new Exception("Unexpected EwsAddress default value");
                    
                    if (processor.NumberOfNodeMappings != 10)
                        throw new Exception("Unexpected NumberOfNodeMappings default value");
                    
                    if (processor.OpcUaEndpointUrl != "opc.tcp://localhost:4840")
                        throw new Exception("Unexpected OpcUaEndpointUrl default value");
                    
                    if (processor.OpcUaTimeoutMs != 5000)
                        throw new Exception("Unexpected OpcUaTimeoutMs default value");
                    
                    if (!processor.AcceptUntrustedCertificates)
                        throw new Exception("Unexpected AcceptUntrustedCertificates default value");
                }
            });

            RunTest("UpdateProcessor_DefaultValues", () =>
            {
                using (var processor = new UpdateProcessor())
                {
                    if (processor.UpdateIntervalSeconds != 5)
                        throw new Exception("Unexpected UpdateIntervalSeconds default value");
                    
                    if (processor.MaxNodeMappings != 50)
                        throw new Exception("Unexpected MaxNodeMappings default value");
                    
                    if (processor.OpcUaEndpointUrl != "opc.tcp://localhost:4840")
                        throw new Exception("Unexpected OpcUaEndpointUrl default value");
                }
            });
        }

        private static void TestDataTypeMappings()
        {
            Console.WriteLine("Testing data type mappings...");

            RunTest("GetEwsValueType_Mappings", () =>
            {
                // Test some basic type mappings
                if (OpcUaHelper.GetEwsValueType(Opc.Ua.BuiltInType.Boolean) != Ews.Common.EwsValueTypeEnum.Boolean)
                    throw new Exception("Boolean type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(Opc.Ua.BuiltInType.Int32) != Ews.Common.EwsValueTypeEnum.Integer)
                    throw new Exception("Int32 type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(Opc.Ua.BuiltInType.Double) != Ews.Common.EwsValueTypeEnum.Double)
                    throw new Exception("Double type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(Opc.Ua.BuiltInType.String) != Ews.Common.EwsValueTypeEnum.String)
                    throw new Exception("String type mapping failed");
                
                if (OpcUaHelper.GetEwsValueType(Opc.Ua.BuiltInType.DateTime) != Ews.Common.EwsValueTypeEnum.DateTime)
                    throw new Exception("DateTime type mapping failed");
            });

            RunTest("GetEwsValueState_Mappings", () =>
            {
                // Test status code mappings
                if (OpcUaHelper.GetEwsValueState(Opc.Ua.StatusCodes.Good) != Ews.Common.EwsValueStateEnum.Good)
                    throw new Exception("Good status mapping failed");
                
                if (OpcUaHelper.GetEwsValueState(Opc.Ua.StatusCodes.UncertainInitialValue) != Ews.Common.EwsValueStateEnum.Uncertain)
                    throw new Exception("Uncertain status mapping failed");
                
                if (OpcUaHelper.GetEwsValueState(Opc.Ua.StatusCodes.BadNodeIdUnknown) != Ews.Common.EwsValueStateEnum.Bad)
                    throw new Exception("Bad status mapping failed");
            });
        }

        private static void RunTest(string testName, Action testAction)
        {
            try
            {
                testAction();
                _passedTests++;
                Console.WriteLine($"  ✓ {testName}");
            }
            catch (Exception ex)
            {
                _failedTests++;
                var failure = $"  ✗ {testName}: {ex.Message}";
                _failures.Add(failure);
                Console.WriteLine(failure);
            }
        }

        private static void PrintResults()
        {
            Console.WriteLine();
            Console.WriteLine("=== Test Results ===");
            Console.WriteLine($"Passed: {_passedTests}");
            Console.WriteLine($"Failed: {_failedTests}");
            Console.WriteLine($"Total:  {_passedTests + _failedTests}");

            if (_failures.Count > 0)
            {
                Console.WriteLine();
                Console.WriteLine("Failures:");
                foreach (var failure in _failures)
                {
                    Console.WriteLine(failure);
                }
            }

            if (_failedTests == 0)
            {
                Console.WriteLine();
                Console.WriteLine("🎉 All tests passed!");
            }
        }
    }
}
