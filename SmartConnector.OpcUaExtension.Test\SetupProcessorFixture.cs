using System;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using NUnit.Framework;
using SmartConnector.OpcUaExtension;

namespace SmartConnector.OpcUaExtension.Test
{
    [TestFixture]
    public class SetupProcessorFixture
    {
        private SetupProcessor _processor;

        [SetUp]
        public void SetUp()
        {
            _processor = new SetupProcessor();
        }

        [TearDown]
        public void TearDown()
        {
            _processor?.Dispose();
        }

        [Test]
        public void Constructor_CreatesInstance_Success()
        {
            // Arrange & Act
            using (var processor = new SetupProcessor())
            {
                // Assert
                Assert.IsNotNull(processor);
                Assert.IsInstanceOf<OpcUaProcessorBase>(processor);
            }
        }

        [Test]
        public void DefaultProperties_HaveExpectedValues()
        {
            // Arrange & Act & Assert
            Assert.AreEqual("SmartConnector OPC UA Service", _processor.ServerName);
            Assert.AreEqual("admin", _processor.UserName);
            Assert.AreEqual("Admin!23", _processor.Password);
            Assert.AreEqual("http://localhost:5400/SmartConnectorOpcUaService", _processor.EwsAddress);
            Assert.AreEqual("OpcUaRealm", _processor.Realm);
            Assert.AreEqual(10, _processor.NumberOfNodeMappings);
            Assert.AreEqual("opc.tcp://localhost:4840", _processor.OpcUaEndpointUrl);
            Assert.AreEqual(5000, _processor.OpcUaTimeoutMs);
            Assert.AreEqual(true, _processor.AcceptUntrustedCertificates);
        }

        [Test]
        public void Validate_ValidConfiguration_NoErrors()
        {
            // Arrange
            _processor.ServerName = "TestServer";
            _processor.UserName = "testuser";
            _processor.Password = "testpass";
            _processor.OpcUaEndpointUrl = "opc.tcp://localhost:4840";

            var context = new ValidationContext(_processor);

            // Act
            var results = _processor.Validate(context).ToList();

            // Assert
            Assert.IsEmpty(results);
        }

        [Test]
        public void Validate_SameUserNameAndPassword_ReturnsError()
        {
            // Arrange
            _processor.UserName = "samevalue";
            _processor.Password = "samevalue";
            _processor.OpcUaEndpointUrl = "opc.tcp://localhost:4840";

            var context = new ValidationContext(_processor);

            // Act
            var results = _processor.Validate(context).ToList();

            // Assert
            Assert.IsNotEmpty(results);
            Assert.IsTrue(results.Any(r => r.ErrorMessage.Contains("UserName and Password cannot be the same")));
        }

        [Test]
        public void Validate_EmptyOpcUaEndpointUrl_ReturnsError()
        {
            // Arrange
            _processor.UserName = "testuser";
            _processor.Password = "testpass";
            _processor.OpcUaEndpointUrl = "";

            var context = new ValidationContext(_processor);

            // Act
            var results = _processor.Validate(context).ToList();

            // Assert
            Assert.IsNotEmpty(results);
            Assert.IsTrue(results.Any(r => r.ErrorMessage.Contains("OPC UA Endpoint URL is required")));
        }

        [Test]
        public void Validate_NullOpcUaEndpointUrl_ReturnsError()
        {
            // Arrange
            _processor.UserName = "testuser";
            _processor.Password = "testpass";
            _processor.OpcUaEndpointUrl = null;

            var context = new ValidationContext(_processor);

            // Act
            var results = _processor.Validate(context).ToList();

            // Assert
            Assert.IsNotEmpty(results);
            Assert.IsTrue(results.Any(r => r.ErrorMessage.Contains("OPC UA Endpoint URL is required")));
        }

        [Test]
        public void NumberOfNodeMappings_SetValue_UpdatesProperty()
        {
            // Arrange
            var expectedValue = 25;

            // Act
            _processor.NumberOfNodeMappings = expectedValue;

            // Assert
            Assert.AreEqual(expectedValue, _processor.NumberOfNodeMappings);
        }

        [Test]
        public void OpcUaTimeoutMs_SetValue_UpdatesProperty()
        {
            // Arrange
            var expectedValue = 10000;

            // Act
            _processor.OpcUaTimeoutMs = expectedValue;

            // Assert
            Assert.AreEqual(expectedValue, _processor.OpcUaTimeoutMs);
        }

        [Test]
        public void AcceptUntrustedCertificates_SetValue_UpdatesProperty()
        {
            // Arrange
            var expectedValue = false;

            // Act
            _processor.AcceptUntrustedCertificates = expectedValue;

            // Assert
            Assert.AreEqual(expectedValue, _processor.AcceptUntrustedCertificates);
        }

        [Test]
        public void OpcUaUserName_SetValue_UpdatesProperty()
        {
            // Arrange
            var expectedValue = "opcuauser";

            // Act
            _processor.OpcUaUserName = expectedValue;

            // Assert
            Assert.AreEqual(expectedValue, _processor.OpcUaUserName);
        }

        [Test]
        public void OpcUaPassword_SetValue_UpdatesProperty()
        {
            // Arrange
            var expectedValue = "opcuapass";

            // Act
            _processor.OpcUaPassword = expectedValue;

            // Assert
            Assert.AreEqual(expectedValue, _processor.OpcUaPassword);
        }

        [Test]
        public void IsLicensed_InDebugMode_ReturnsFalse()
        {
            // Arrange & Act & Assert
#if DEBUG
            Assert.IsFalse(_processor.IsLicensed);
#else
            // In release mode, this test would need to be adjusted based on actual licensing logic
            Assert.Pass("Test only applies in DEBUG mode");
#endif
        }
    }
}
