﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A TrendSample represents the value and state for Value at a given
    /// point in time belonging to a specific Trend.
    /// </summary>
    public partial class TrendSampleModel
    {
        /// <summary>
        /// Initializes a new instance of the TrendSampleModel class.
        /// </summary>
        public TrendSampleModel() { }

        /// <summary>
        /// Initializes a new instance of the TrendSampleModel class.
        /// </summary>
        public TrendSampleModel(string trendId = default(string), string value = default(string), string state = default(string), DateTime? sampleDate = default(DateTime?), string id = default(string))
        {
            TrendId = trendId;
            Value = value;
            State = state;
            SampleDate = sampleDate;
            Id = id;
        }

        /// <summary>
        /// Id of the Trend this is a sample for.
        /// </summary>
        [JsonProperty(PropertyName = "TrendId")]
        public string TrendId { get; set; }

        /// <summary>
        /// Value of the Trend.Value at the time of the sample.
        /// </summary>
        [JsonProperty(PropertyName = "Value")]
        public string Value { get; set; }

        /// <summary>
        /// State of the Trend.Value at the time of the sample. Possible
        /// values include: 'Good', 'Uncertain', 'Forced', 'Offline', 'Error'
        /// </summary>
        [JsonProperty(PropertyName = "State")]
        public string State { get; set; }

        /// <summary>
        /// DateTime in UTC when the sample was created.
        /// </summary>
        [JsonProperty(PropertyName = "SampleDate")]
        public DateTime? SampleDate { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
