using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Threading.Tasks;
using Mongoose.Common;
using Mongoose.Common.Attributes;
using Mongoose.Ews.Server.Data;
using Mongoose.Process;
using SxL.Common;
using Opc.Ua;
using Opc.Ua.Client;

namespace SmartConnector.OpcUaExtension
{
    /// <summary>
    /// Common base class for OPC UA processors. Contains the common configuration properties and OPC UA client management.
    /// </summary>
    public abstract class OpcUaProcessorBase : Processor
    {
#if DEBUG
        public override bool IsLicensed => false;
#endif

        #region ServerName
        [Required, DefaultValue("SmartConnector OPC UA Service"), Tooltip("Name of the EWS Server to connect to or bootstrap")]
        public string ServerName { get; set; }
        #endregion

        #region UserName
        [Required, EncryptedString, DefaultValue("admin")]
        public string UserName { get; set; }
        #endregion

        #region Password
        [Required, EncryptedString, DefaultValue("Admin!23")]
        public string Password { get; set; }
        #endregion

        #region OPC UA Connection Properties
        [Required, DefaultValue("opc.tcp://localhost:4840"), Tooltip("OPC UA server endpoint URL")]
        public string OpcUaEndpointUrl { get; set; }

        [DefaultValue(""), Tooltip("OPC UA username (leave empty for anonymous)")]
        public string OpcUaUserName { get; set; }

        [EncryptedString, DefaultValue(""), Tooltip("OPC UA password (leave empty for anonymous)")]
        public string OpcUaPassword { get; set; }

        [DefaultValue(5000), Tooltip("OPC UA connection timeout in milliseconds")]
        public int OpcUaTimeoutMs { get; set; } = 5000;

        [DefaultValue(true), Tooltip("Accept untrusted certificates")]
        public bool AcceptUntrustedCertificates { get; set; } = true;
        #endregion

        #region Validate - Override
        public override IEnumerable<ValidationResult> Validate(ValidationContext validationContext)
        {
            if (!string.IsNullOrEmpty(UserName) && !string.IsNullOrEmpty(Password) && UserName == Password)
            {
                yield return new ValidationResult("UserName and Password cannot be the same", new[] { "UserName", "Password" });
            }

            if (string.IsNullOrEmpty(OpcUaEndpointUrl))
            {
                yield return new ValidationResult("OPC UA Endpoint URL is required", new[] { "OpcUaEndpointUrl" });
            }

            foreach (var validationResult in base.Validate(validationContext))
            {
                yield return validationResult;
            }
        }
        #endregion

        #region DataAdapter
        private EwsServerDataAdapter _dataAdapter = null;
        /// <summary>
        /// Lazily created instance of an EwsServerDataAdapter
        /// </summary>
        protected EwsServerDataAdapter DataAdapter
        {
            get
            {
                if (_dataAdapter != null) return _dataAdapter;
                try
                {
                    _dataAdapter = EwsServerDataAdapter.ConnectExisting(ServerName, UserName, Password);
                }
                catch (ApplicationException ex)
                {
                    // We'll log the error, but continue on
                    Logger.LogError(LogCategory.Processor, ex);
                }
                return _dataAdapter ?? (_dataAdapter = CreateEwsServer());
            }
            set { _dataAdapter = value; }
        }
        #endregion

        #region OPC UA Client
        private Session _opcUaSession = null;
        private ApplicationConfiguration _opcUaConfig = null;

        /// <summary>
        /// Gets the OPC UA session, creating it if necessary
        /// </summary>
        protected Session OpcUaSession
        {
            get
            {
                if (_opcUaSession != null && _opcUaSession.Connected) return _opcUaSession;
                try
                {
                    _opcUaSession = CreateOpcUaSession().Result;
                }
                catch (Exception ex)
                {
                    Logger.LogError(LogCategory.Processor, ex);
                    _opcUaSession = null;
                }
                return _opcUaSession;
            }
        }

        /// <summary>
        /// Creates and connects to an OPC UA session
        /// </summary>
        private async Task<Session> CreateOpcUaSession()
        {
            if (_opcUaConfig == null)
            {
                _opcUaConfig = await CreateOpcUaConfiguration();
            }

            var endpoint = CoreClientUtils.SelectEndpoint(OpcUaEndpointUrl, false, OpcUaTimeoutMs);
            var endpointConfiguration = EndpointConfiguration.Create(_opcUaConfig);
            var endpoint2 = new ConfiguredEndpoint(null, endpoint, endpointConfiguration);

            UserIdentity userIdentity = null;
            if (!string.IsNullOrEmpty(OpcUaUserName))
            {
                userIdentity = new UserIdentity(OpcUaUserName, OpcUaPassword);
            }

            var session = await Session.Create(_opcUaConfig, endpoint2, false, "SmartConnector OPC UA Client", 60000, userIdentity, null);
            return session;
        }

        /// <summary>
        /// Creates the OPC UA application configuration
        /// </summary>
        private async Task<ApplicationConfiguration> CreateOpcUaConfiguration()
        {
            var config = new ApplicationConfiguration()
            {
                ApplicationName = "SmartConnector OPC UA Client",
                ApplicationUri = Utils.Format(@"urn:{0}:SmartConnectorOpcUaClient", System.Net.Dns.GetHostName()),
                ApplicationType = ApplicationType.Client,
                SecurityConfiguration = new SecurityConfiguration
                {
                    ApplicationCertificate = new CertificateIdentifier { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\MachineDefault", SubjectName = Utils.Format(@"CN={0}, DC={1}", "SmartConnectorOpcUaClient", System.Net.Dns.GetHostName()) },
                    TrustedIssuerCertificates = new CertificateTrustList { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\UA Certificate Authorities" },
                    TrustedPeerCertificates = new CertificateTrustList { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\UA Applications" },
                    RejectedCertificateStore = new CertificateTrustList { StoreType = @"Directory", StorePath = @"%CommonApplicationData%\OPC Foundation\CertificateStores\RejectedCertificates" },
                    AutoAcceptUntrustedCertificates = AcceptUntrustedCertificates
                },
                TransportConfigurations = new TransportConfigurationCollection(),
                TransportQuotas = new TransportQuotas { OperationTimeout = OpcUaTimeoutMs },
                ClientConfiguration = new ClientConfiguration { DefaultSessionTimeout = 60000 },
                TraceConfiguration = new TraceConfiguration()
            };

            config.Validate(ApplicationType.Client).GetAwaiter().GetResult();
            if (config.SecurityConfiguration.AutoAcceptUntrustedCertificates)
            {
                config.CertificateValidator.CertificateValidation += (s, e) => { e.Accept = (e.Error.StatusCode == StatusCodes.BadCertificateUntrusted); };
            }

            return config;
        }
        #endregion

        #region IsConnected
        /// <summary>
        /// Returns true if DataAdapter has connected to an EWS Server and OPC UA session is connected
        /// </summary>
        protected bool IsConnected => DataAdapter != null && OpcUaSession != null && OpcUaSession.Connected;
        #endregion

        #region IsEwsConnected
        /// <summary>
        /// Returns true if DataAdapter has connected to an EWS Server
        /// </summary>
        protected bool IsEwsConnected => DataAdapter != null;
        #endregion

        #region IsOpcUaConnected
        /// <summary>
        /// Returns true if OPC UA session is connected
        /// </summary>
        protected bool IsOpcUaConnected => OpcUaSession != null && OpcUaSession.Connected;
        #endregion

        #region CreateEwsServer - Virtual
        protected virtual EwsServerDataAdapter CreateEwsServer()
        {
            return null;
        }
        #endregion

        #region CreateCannotConnectPrompt
        protected Prompt CreateCannotConnectPrompt()
        {
            return new Prompt
            {
                Message = "Failed to connect to a valid EwsServerDataAdapter.",
                Severity = PromptSeverity.MayNotContinue
            };
        }
        #endregion

        #region CreateCannotConnectOpcUaPrompt
        protected Prompt CreateCannotConnectOpcUaPrompt()
        {
            return new Prompt
            {
                Message = "Failed to connect to OPC UA server.",
                Severity = PromptSeverity.MayNotContinue
            };
        }
        #endregion

        #region Dispose - Override
        private bool _disposed;
        protected override void Dispose(bool disposing)
        {
            base.Dispose(disposing);
            if (_disposed) return;
            
            try
            {
                _opcUaSession?.Close();
                _opcUaSession?.Dispose();
            }
            catch (Exception ex)
            {
                Logger.LogError(LogCategory.Processor, ex);
            }
            
            _dataAdapter?.Dispose();
            _disposed = true;
        }
        #endregion
    }
}
