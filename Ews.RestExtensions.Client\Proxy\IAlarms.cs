﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections.Generic;
    using System.Net.Http;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Alarms operations.
    /// </summary>
    public partial interface IAlarms
    {
        /// <summary>
        /// Creates a new Alarm and returns it.
        /// </summary>
        /// <param name='newItem'>
        /// Definition of how the Alarm will be created.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> CreateWithHttpMessagesAsync(NewAlarmModel newItem, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns all Alarms which fit the filter criteria supplied.  All
        /// filter parameters are optional.  Alarms will be ordered and paged
        /// as requested.
        /// </summary>
        /// <param name='name'>
        /// Return only Alarm whose name contains this value.
        /// </param>
        /// <param name='state'>
        /// Return only Alarm with this State value. Possible values include:
        /// 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
        /// </param>
        /// <param name='orderBy'>
        /// Alarm will be returned in this order.  TransitionedAtAscending by
        /// default. Possible values include: 'NameAscending',
        /// 'NameDescending'
        /// </param>
        /// <param name='take'>
        /// Number of Alarm that should be returned.  200 by default.
        /// </param>
        /// <param name='skip'>
        /// Number of Alarm that should be skipped before items are returned.
        /// 0 by default.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<IList<AlarmModel>>> RetrieveWithHttpMessagesAsync(string name = default(string), string state = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm requested.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> RetrieveByIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Description property for the Alarm with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose Description property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveDescriptionWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Description property for the Alarm with the Id
        /// provided to the new value supplied.  Returns the modified Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose Description property is to be modified.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Description value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> UpdateDescriptionWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the Name property for the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose Name property is to be returned.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveNameWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Name property for the Alarm with the Id provided to
        /// the new value supplied.  Returns the modified Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose Name property is to be modified.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Name value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> UpdateNameWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the parent Container for the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose parent Container is to be returned.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ContainerModel>> RetrieveParentWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the ParentId property for the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose ParentId property is to be returned.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveParentIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the ParentId property for the Alarm with the Id provided
        /// to the new value supplied.  Returns the modified Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose ParentId property is to be modified.  Must
        /// be double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New ParentId value
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> UpdateParentIdWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the State property for the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose State property is to be returned.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveStateWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the TransitionModel property for the Alarm with the Id
        /// provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose TransitionModel property is to be returned.
        /// Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveTransitionModelWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the associated Value for the Alarm with the Id supplied.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm who�s associated Value is to be returned.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<ValueModel>> RetrieveValueWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Returns the ValueId property for the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose ValueId property is to be returned.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> RetrieveValueIdWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Alarm with the Id provided to the values supplied.
        /// Returns the modified Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be modified.  Must be double URL encoded.
        /// </param>
        /// <param name='updatedItem'>
        /// Item to be updated.  All modfied properties will be changed.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> UpdateWithHttpMessagesAsync(string id, UpdateAlarmModel updatedItem, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Modifies the Id property for the Alarm with the Id provided to the
        /// new value supplied.  Returns the modified Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm whose Id property is to be modified.  Must be
        /// double URL encoded.
        /// </param>
        /// <param name='newValue'>
        /// New Id value.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<string>> UpdateIdWithHttpMessagesAsync(string id, string newValue, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Executes an Acknowledge action on the Alarm with the Id provided.
        /// The result will generate a new AlarmEvent based on the
        /// information contained in the newEvent parameter.
        /// Restrictions apply based on the TransitionModel of the Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
        /// </param>
        /// <param name='newEvent'>
        /// Information to describe the generated AlarmEvent.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> AcknowledgeWithHttpMessagesAsync(string id, AlarmEventInfo newEvent, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Executes an Activate action on the Alarm with the Id provided.
        /// The result will generate a new AlarmEvent based on the
        /// information contained in the newEvent parameter.
        /// Restrictions apply based on the TransitionModel of the Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
        /// </param>
        /// <param name='newEvent'>
        /// Information to describe the generated AlarmEvent.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> ActivateWithHttpMessagesAsync(string id, AlarmEventInfo newEvent, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Executes a Disable action on the Alarm with the Id provided.  The
        /// result will generate a new AlarmEvent based on the information
        /// contained in the newEvent parameter.
        /// Restrictions apply based on the TransitionModel of the Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
        /// </param>
        /// <param name='newEvent'>
        /// Information to describe the generated AlarmEvent.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> DisableWithHttpMessagesAsync(string id, AlarmEventInfo newEvent, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Executes a Reset action on the Alarm with the Id provided.  The
        /// result will generate a new AlarmEvent based on the information
        /// contained in the newEvent parameter.
        /// Restrictions apply based on the TransitionModel of the Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
        /// </param>
        /// <param name='newEvent'>
        /// Information to describe the generated AlarmEvent.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> ResetWithHttpMessagesAsync(string id, AlarmEventInfo newEvent, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Executes a manual state change action on the Alarm with the Id
        /// provided (Alarm must have an Unrestricted TransitionModel).  The
        /// result will generate a new AlarmEvent based on the information
        /// contained in the newEvent parameter.
        /// Restrictions apply based on the TransitionModel of the Alarm.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be Acknowledged.  Must be double URL encoded.
        /// </param>
        /// <param name='newEvent'>
        /// Information to describe the generated AlarmEvent.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<AlarmModel>> ChangeStateWithHttpMessagesAsync(string id, AlarmEventStateChangeInfo newEvent, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
        /// <summary>
        /// Deletes the Alarm with the Id provided.
        /// </summary>
        /// <param name='id'>
        /// Id of the Alarm to be deleted.  Must be double URL encoded.
        /// </param>
        /// <param name='customHeaders'>
        /// The headers that will be added to request.
        /// </param>
        /// <param name='cancellationToken'>
        /// The cancellation token.
        /// </param>
        Task<HttpOperationResponse<object>> DeleteWithHttpMessagesAsync(string id, Dictionary<string, List<string>> customHeaders = null, CancellationToken cancellationToken = default(CancellationToken));
    }
}
