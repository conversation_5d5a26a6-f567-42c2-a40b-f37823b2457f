<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{F8E2A1B3-4C5D-4E6F-8A9B-1C2D3E4F5A6B}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SmartConnector.OpcUaExtension</RootNamespace>
    <AssemblyName>SmartConnector.OpcUaExtension</AssemblyName>
    <TargetFrameworkVersion>v4.6.1</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="EntityFramework, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.dll</HintPath>
    </Reference>
    <Reference Include="EntityFramework.SqlServer, Version=6.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089, processorArchitecture=MSIL">
      <HintPath>..\packages\EntityFramework.6.2.0\lib\net45\EntityFramework.SqlServer.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Client, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Ews.Client.2.4.10\lib\net452\Ews.Client.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Common, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Ews.Common.2.4.10\lib\net452\Ews.Common.dll</HintPath>
    </Reference>
    <Reference Include="Ews.Server.Contract, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Ews.Server.Contract.2.4.10\lib\net452\Ews.Server.Contract.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Core, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Core.2.2.1\lib\net45\Microsoft.AspNet.Identity.Core.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.AspNet.Identity.Owin, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.Identity.Owin.2.2.1\lib\net45\Microsoft.AspNet.Identity.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="Microsoft.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.3.1.0\lib\net45\Microsoft.Owin.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Host.HttpListener, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Host.HttpListener.3.1.0\lib\net45\Microsoft.Owin.Host.HttpListener.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Hosting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Hosting.3.1.0\lib\net45\Microsoft.Owin.Hosting.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.3.1.0\lib\net45\Microsoft.Owin.Security.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.Cookies, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.Cookies.3.1.0\lib\net45\Microsoft.Owin.Security.Cookies.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Owin.Security.OAuth, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Owin.Security.OAuth.3.1.0\lib\net45\Microsoft.Owin.Security.OAuth.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Common, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Common.2.4.10\lib\net452\Mongoose.Common.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Ews.Server, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Ews.Server.2.4.10\lib\net452\Mongoose.Ews.Server.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Ews.Server.Data, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Ews.Server.Data.2.4.10\lib\net452\Mongoose.Ews.Server.Data.dll</HintPath>
    </Reference>
    <Reference Include="Mongoose.Process, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\Mongoose.Process.2.4.10\lib\net452\Mongoose.Process.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="NLog, Version=4.0.0.0, Culture=neutral, PublicKeyToken=5120e14c03d0593c, processorArchitecture=MSIL">
      <HintPath>..\packages\NLog.4.4.12\lib\net45\NLog.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Client, Version=1.4.371.86, Culture=neutral, PublicKeyToken=96c1b444d7e4b8b9, processorArchitecture=MSIL">
      <HintPath>..\packages\OPCFoundation.NetStandard.Opc.Ua.Client.1.4.371.86\lib\net462\Opc.Ua.Client.dll</HintPath>
    </Reference>
    <Reference Include="Opc.Ua.Core, Version=1.4.371.86, Culture=neutral, PublicKeyToken=96c1b444d7e4b8b9, processorArchitecture=MSIL">
      <HintPath>..\packages\OPCFoundation.NetStandard.Opc.Ua.1.4.371.86\lib\net462\Opc.Ua.Core.dll</HintPath>
    </Reference>
    <Reference Include="Owin, Version=1.0.0.0, Culture=neutral, PublicKeyToken=f0ebd12fd5e55cc5, processorArchitecture=MSIL">
      <HintPath>..\packages\Owin.1.0\lib\net40\Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="SmartConnector.Utilities, Version=1.0.0.4, Culture=neutral, processorArchitecture=MSIL">
      <HintPath>..\packages\SmartConnector.Utilities.1.0.0.4\lib\net461\SmartConnector.Utilities.dll</HintPath>
    </Reference>
    <Reference Include="Swashbuckle.Core, Version=1.0.0.0, Culture=neutral, PublicKeyToken=cd1bb07a5ac7c7bc, processorArchitecture=MSIL">
      <HintPath>..\packages\Swashbuckle.Core.5.6.0\lib\net40\Swashbuckle.Core.dll</HintPath>
    </Reference>
    <Reference Include="SxL.Common, Version=********, Culture=neutral, PublicKeyToken=223885051a9a1eff, processorArchitecture=MSIL">
      <HintPath>..\packages\SxL.Common.2.4.10\lib\net452\SxL.Common.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.ComponentModel.DataAnnotations" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net.Http.Formatting, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net461\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web.Http, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Core.5.2.3\lib\net45\System.Web.Http.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.Owin, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Owin.5.2.3\lib\net45\System.Web.Http.Owin.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Http.WebHost, Version=*******, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.WebHost.5.2.3\lib\net45\System.Web.Http.WebHost.dll</HintPath>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
    <Reference Include="WebApiThrottle.StrongName, Version=*******, Culture=neutral, PublicKeyToken=82ddef9bb9708c54, processorArchitecture=MSIL">
      <HintPath>..\packages\WebApiThrottle.StrongName.1.5.4\lib\net45\WebApiThrottle.StrongName.dll</HintPath>
    </Reference>
  </ItemGroup>
  <ItemGroup>
    <Compile Include="EwsServer\CustomDataExchange.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="EwsServer\CustomEwsServiceHost.cs" />
    <Compile Include="EwsServer\CustomSetValuesProcessor.cs" />
    <Compile Include="OpcUaHelper.cs" />
    <Compile Include="OpcUaProcessorBase.cs" />
    <Compile Include="UpdateProcessor.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="SetupProcessor.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="App.config">
      <SubType>Designer</SubType>
    </None>
    <None Include="packages.config">
      <SubType>Designer</SubType>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>
