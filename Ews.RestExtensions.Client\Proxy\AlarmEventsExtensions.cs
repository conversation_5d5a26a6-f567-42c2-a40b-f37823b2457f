﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for AlarmEvents.
    /// </summary>
    public static partial class AlarmEventsExtensions
    {
            /// <summary>
            /// Returns all AlarmEvents which fit the filter criteria supplied.  All
            /// filter parameters are optional.  AlarmEvents will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='alarmId'>
            /// Return only AlarmEvent for the referenced Alarm.
            /// </param>
            /// <param name='priorityFrom'>
            /// Return only AlarmEvent with a Priority greater or equal to the value
            /// supplied.
            /// </param>
            /// <param name='priorityTo'>
            /// Return only AlarmEvent with a Priority less or equal to the value supplied.
            /// </param>
            /// <param name='state'>
            /// Return only AlarmEvent with this State value. Possible values include:
            /// 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
            /// </param>
            /// <param name='acknowledgeable'>
            /// Return only AlarmEvent with this Acknowledgeable value. Possible values
            /// include: 'No', 'Yes', 'Required'
            /// </param>
            /// <param name='type'>
            /// Return only AlarmEvent with this Type value.
            /// </param>
            /// <param name='originallyAlarmedOnOrAfter'>
            /// Return only AlarmEvent with OccuredOn which is on or after this value.
            /// </param>
            /// <param name='originallyAlarmedBefore'>
            /// Return only AlarmEvent with OccuredOn which is before this value.
            /// </param>
            /// <param name='transitionedOnOrAfter'>
            /// Return only AlarmEvent with LastTransitionedOn which is on or after this
            /// value.
            /// </param>
            /// <param name='transitionedBefore'>
            /// Return only AlarmEvent with LastTransitionedOn which is before this value.
            /// </param>
            /// <param name='orderBy'>
            /// AlarmEvent will be returned in this order.  TransitionedAtAscending by
            /// default. Possible values include: 'TransitionedAtAscending',
            /// 'TransitionedAtDescending', 'OriginallyAlarmedAtAscending',
            /// 'OriginallyAlarmedAtDescending'
            /// </param>
            /// <param name='take'>
            /// Number of AlarmEvent that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of AlarmEvent that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            public static IList<AlarmEventModel> Retrieve(this IAlarmEvents operations, string alarmId = default(string), int? priorityFrom = default(int?), int? priorityTo = default(int?), string state = default(string), string acknowledgeable = default(string), string type = default(string), DateTime? originallyAlarmedOnOrAfter = default(DateTime?), DateTime? originallyAlarmedBefore = default(DateTime?), DateTime? transitionedOnOrAfter = default(DateTime?), DateTime? transitionedBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveAsync(alarmId, priorityFrom, priorityTo, state, acknowledgeable, type, originallyAlarmedOnOrAfter, originallyAlarmedBefore, transitionedOnOrAfter, transitionedBefore, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all AlarmEvents which fit the filter criteria supplied.  All
            /// filter parameters are optional.  AlarmEvents will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='alarmId'>
            /// Return only AlarmEvent for the referenced Alarm.
            /// </param>
            /// <param name='priorityFrom'>
            /// Return only AlarmEvent with a Priority greater or equal to the value
            /// supplied.
            /// </param>
            /// <param name='priorityTo'>
            /// Return only AlarmEvent with a Priority less or equal to the value supplied.
            /// </param>
            /// <param name='state'>
            /// Return only AlarmEvent with this State value. Possible values include:
            /// 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
            /// </param>
            /// <param name='acknowledgeable'>
            /// Return only AlarmEvent with this Acknowledgeable value. Possible values
            /// include: 'No', 'Yes', 'Required'
            /// </param>
            /// <param name='type'>
            /// Return only AlarmEvent with this Type value.
            /// </param>
            /// <param name='originallyAlarmedOnOrAfter'>
            /// Return only AlarmEvent with OccuredOn which is on or after this value.
            /// </param>
            /// <param name='originallyAlarmedBefore'>
            /// Return only AlarmEvent with OccuredOn which is before this value.
            /// </param>
            /// <param name='transitionedOnOrAfter'>
            /// Return only AlarmEvent with LastTransitionedOn which is on or after this
            /// value.
            /// </param>
            /// <param name='transitionedBefore'>
            /// Return only AlarmEvent with LastTransitionedOn which is before this value.
            /// </param>
            /// <param name='orderBy'>
            /// AlarmEvent will be returned in this order.  TransitionedAtAscending by
            /// default. Possible values include: 'TransitionedAtAscending',
            /// 'TransitionedAtDescending', 'OriginallyAlarmedAtAscending',
            /// 'OriginallyAlarmedAtDescending'
            /// </param>
            /// <param name='take'>
            /// Number of AlarmEvent that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of AlarmEvent that should be skipped before items are returned.  0
            /// by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<AlarmEventModel>> RetrieveAsync(this IAlarmEvents operations, string alarmId = default(string), int? priorityFrom = default(int?), int? priorityTo = default(int?), string state = default(string), string acknowledgeable = default(string), string type = default(string), DateTime? originallyAlarmedOnOrAfter = default(DateTime?), DateTime? originallyAlarmedBefore = default(DateTime?), DateTime? transitionedOnOrAfter = default(DateTime?), DateTime? transitionedBefore = default(DateTime?), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(alarmId, priorityFrom, priorityTo, state, acknowledgeable, type, originallyAlarmedOnOrAfter, originallyAlarmedBefore, transitionedOnOrAfter, transitionedBefore, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent requested.  Must be double URL encoded.
            /// </param>
            public static AlarmEventModel RetrieveById(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmEventModel> RetrieveByIdAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the AlarmId property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose AlarmId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveAlarmId(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveAlarmIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the AlarmId property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose AlarmId property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveAlarmIdAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveAlarmIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the referenced Alarm for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent for which the referenced Alarm is to be returned.
            /// Value must be double URL encoded
            /// </param>
            public static AlarmModel RetrieveAlarm(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveAlarmAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the referenced Alarm for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent for which the referenced Alarm is to be returned.
            /// Value must be double URL encoded
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<AlarmModel> RetrieveAlarmAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveAlarmWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Acknowledgeable property for the AlarmEvent with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Acknowledgeable property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            public static string RetrieveAcknowledgeable(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveAcknowledgeableAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Acknowledgeable property for the AlarmEvent with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Acknowledgeable property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveAcknowledgeableAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveAcknowledgeableWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the OccurredOn property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose OccurredOn property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static DateTime? RetrieveOccurredOn(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveOccurredOnAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the OccurredOn property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose OccurredOn property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<DateTime?> RetrieveOccurredOnAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveOccurredOnWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the LastTransitionedOn property for the AlarmEvent with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose LastTransitionedOn property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            public static DateTime? RetrieveLastTransitionedOn(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveLastTransitionedOnAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the LastTransitionedOn property for the AlarmEvent with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose LastTransitionedOn property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<DateTime?> RetrieveLastTransitionedOnAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveLastTransitionedOnWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Message property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Message property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveMessage(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveMessageAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Message property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Message property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveMessageAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveMessageWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Priority property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Priority property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static int? RetrievePriority(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrievePriorityAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Priority property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Priority property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<int?> RetrievePriorityAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrievePriorityWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the State property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose State property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveState(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveStateAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the State property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose State property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveStateAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveStateWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Type property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Type property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveType(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).RetrieveTypeAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Type property for the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent whose Type property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveTypeAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTypeWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Executes an Acknowledge operation on the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent to acknowledge.  Must be double URL encoded.
            /// </param>
            public static object Acknowledge(this IAlarmEvents operations, string id)
            {
                return Task.Factory.StartNew(s => ((IAlarmEvents)s).AcknowledgeAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Executes an Acknowledge operation on the AlarmEvent with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the AlarmEvent to acknowledge.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> AcknowledgeAsync(this IAlarmEvents operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.AcknowledgeWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
