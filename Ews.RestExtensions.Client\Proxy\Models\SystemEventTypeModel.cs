﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// Defines a "Type" for an SystemEvent.
    /// </summary>
    public partial class SystemEventTypeModel
    {
        /// <summary>
        /// Initializes a new instance of the SystemEventTypeModel class.
        /// </summary>
        public SystemEventTypeModel() { }

        /// <summary>
        /// Initializes a new instance of the SystemEventTypeModel class.
        /// </summary>
        public SystemEventTypeModel(string name = default(string), string description = default(string), string id = default(string))
        {
            Name = name;
            Description = description;
            Id = id;
        }

        /// <summary>
        /// Name of the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Optional description of the SystemEventType.
        /// </summary>
        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
