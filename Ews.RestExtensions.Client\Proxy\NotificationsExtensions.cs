﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for Notifications.
    /// </summary>
    public static partial class NotificationsExtensions
    {
            /// <summary>
            /// Creates a new Notification and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Notification will be created.
            /// </param>
            public static NotificationModel Create(this INotifications operations, NewNotificationModel newItem)
            {
                return Task.Factory.StartNew(s => ((INotifications)s).CreateAsync(newItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Creates a new Notification and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Notification will be created.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<NotificationModel> CreateAsync(this INotifications operations, NewNotificationModel newItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(newItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all Notification which fit the filter criteria supplied. All
            /// filter parameters are optional. Notification will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='subscriptionId'>
            /// Return only Notification with this SubscriptionId value.
            /// </param>
            /// <param name='orderBy'>
            /// Notification will be returned in this order.  CreatedOnAscending by
            /// default. Possible values include: 'CreatedOnAscending',
            /// 'CreatedOnDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Notification that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Notification that should be skipped before items are returned.
            /// 0 by default.
            /// </param>
            public static IList<NotificationModel> Retrieve(this INotifications operations, string subscriptionId = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((INotifications)s).RetrieveAsync(subscriptionId, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all Notification which fit the filter criteria supplied. All
            /// filter parameters are optional. Notification will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='subscriptionId'>
            /// Return only Notification with this SubscriptionId value.
            /// </param>
            /// <param name='orderBy'>
            /// Notification will be returned in this order.  CreatedOnAscending by
            /// default. Possible values include: 'CreatedOnAscending',
            /// 'CreatedOnDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Notification that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Notification that should be skipped before items are returned.
            /// 0 by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<NotificationModel>> RetrieveAsync(this INotifications operations, string subscriptionId = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(subscriptionId, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Notification with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification requested.  Must be double URL encoded.
            /// </param>
            public static NotificationModel RetrieveById(this INotifications operations, string id)
            {
                return Task.Factory.StartNew(s => ((INotifications)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Notification with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<NotificationModel> RetrieveByIdAsync(this INotifications operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the CreatedOn property for the Notification with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose CreatedOn property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            public static string RetrieveCreatedOn(this INotifications operations, string id)
            {
                return Task.Factory.StartNew(s => ((INotifications)s).RetrieveCreatedOnAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the CreatedOn property for the Notification with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose CreatedOn property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveCreatedOnAsync(this INotifications operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveCreatedOnWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns NotificationItem for the Notification with the Id provided.
            /// Sort Order for the returned items depends on Subscription Type.  For
            /// ValueItemChanged and HierarchyChanged, the sort order is by ChangedAt.
            /// For AlarmEventChanged, the sort order is by ChangedItemId.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose items are to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='take'>
            /// Number of NotificationItems that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of NotificationItems that should be skipped before items are
            /// returned.  0 by default.
            /// </param>
            public static IList<NotificationItemModel> RetrieveNotifications(this INotifications operations, string id, int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((INotifications)s).RetrieveNotificationsAsync(id, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns NotificationItem for the Notification with the Id provided.
            /// Sort Order for the returned items depends on Subscription Type.  For
            /// ValueItemChanged and HierarchyChanged, the sort order is by ChangedAt.
            /// For AlarmEventChanged, the sort order is by ChangedItemId.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose items are to be returned.  Must be double URL
            /// encoded.
            /// </param>
            /// <param name='take'>
            /// Number of NotificationItems that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of NotificationItems that should be skipped before items are
            /// returned.  0 by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<NotificationItemModel>> RetrieveNotificationsAsync(this INotifications operations, string id, int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveNotificationsWithHttpMessagesAsync(id, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Subscription for the Notification with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose Subscription is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static SubscriptionModel RetrieveSubscription(this INotifications operations, string id)
            {
                return Task.Factory.StartNew(s => ((INotifications)s).RetrieveSubscriptionAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Subscription for the Notification with the Id supplied.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose Subscription is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<SubscriptionModel> RetrieveSubscriptionAsync(this INotifications operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveSubscriptionWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the SubscriptionId property for the Notification with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose SubscriptionId property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            public static string RetrieveSubscriptionId(this INotifications operations, string id)
            {
                return Task.Factory.StartNew(s => ((INotifications)s).RetrieveSubscriptionIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the SubscriptionId property for the Notification with the Id
            /// provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification whose SubscriptionId property is to be returned.
            /// Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveSubscriptionIdAsync(this INotifications operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveSubscriptionIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes the Notification with the Id provided.  All NotificationItems will
            /// also be deleted.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification to be deleted.  Must be double URL encoded.
            /// </param>
            public static object Delete(this INotifications operations, string id)
            {
                return Task.Factory.StartNew(s => ((INotifications)s).DeleteAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes the Notification with the Id provided.  All NotificationItems will
            /// also be deleted.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Notification to be deleted.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteAsync(this INotifications operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
