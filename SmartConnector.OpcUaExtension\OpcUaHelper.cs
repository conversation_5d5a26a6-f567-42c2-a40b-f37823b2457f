using System;
using System.Collections.Generic;
using System.Linq;
using Opc.Ua;

namespace SmartConnector.OpcUaExtension
{
    /// <summary>
    /// Helper class containing constants and utility methods for OPC UA operations
    /// </summary>
    public static class OpcUaHelper
    {
        #region Node IDs and Constants
        
        // Configuration node IDs
        public const string OpcUaEndpointId = "OpcUaEndpoint";
        public const string OpcUaConnectionStatusId = "OpcUaConnectionStatus";
        public const string OpcUaLastConnectedId = "OpcUaLastConnected";
        public const string OpcUaErrorMessageId = "OpcUaErrorMessage";

        // Default node mappings - these can be configured by users
        public const string DefaultNodeMappingsContainerId = "NodeMappings";
        public const string NodeMappingTemplateId = "NodeMapping_{0}";
        public const string NodeMappingNodeIdId = "NodeMapping_{0}_NodeId";
        public const string NodeMappingValueId = "NodeMapping_{0}_Value";
        public const string NodeMappingQualityId = "NodeMapping_{0}_Quality";
        public const string NodeMappingTimestampId = "NodeMapping_{0}_Timestamp";
        public const string NodeMappingHistoryId = "NodeMapping_{0}_History";

        // Default number of node mappings to create
        public const int DefaultNumberOfNodeMappings = 10;

        #endregion

        #region Utility Methods

        /// <summary>
        /// Converts an OPC UA data value to an appropriate .NET type for EWS
        /// </summary>
        /// <param name="dataValue">The OPC UA data value</param>
        /// <returns>The converted value</returns>
        public static object ConvertOpcUaValue(DataValue dataValue)
        {
            if (dataValue?.Value == null) return null;

            var value = dataValue.Value;
            
            // Handle different OPC UA types
            switch (value)
            {
                case bool boolValue:
                    return boolValue;
                case byte byteValue:
                    return (int)byteValue;
                case sbyte sbyteValue:
                    return (int)sbyteValue;
                case short shortValue:
                    return (int)shortValue;
                case ushort ushortValue:
                    return (int)ushortValue;
                case int intValue:
                    return intValue;
                case uint uintValue:
                    return (long)uintValue;
                case long longValue:
                    return longValue;
                case ulong ulongValue:
                    return (long)ulongValue;
                case float floatValue:
                    return (double)floatValue;
                case double doubleValue:
                    return doubleValue;
                case string stringValue:
                    return stringValue;
                case DateTime dateTimeValue:
                    return dateTimeValue;
                case Guid guidValue:
                    return guidValue.ToString();
                case NodeId nodeIdValue:
                    return nodeIdValue.ToString();
                case QualifiedName qualifiedNameValue:
                    return qualifiedNameValue.ToString();
                case LocalizedText localizedTextValue:
                    return localizedTextValue.Text;
                case StatusCode statusCodeValue:
                    return statusCodeValue.ToString();
                case byte[] byteArrayValue:
                    return Convert.ToBase64String(byteArrayValue);
                default:
                    // For complex types, convert to string
                    return value.ToString();
            }
        }

        /// <summary>
        /// Gets the EWS value type that best matches the OPC UA data type
        /// </summary>
        /// <param name="opcUaType">The OPC UA built-in type</param>
        /// <returns>The corresponding EWS value type</returns>
        public static Ews.Common.EwsValueTypeEnum GetEwsValueType(BuiltInType opcUaType)
        {
            switch (opcUaType)
            {
                case BuiltInType.Boolean:
                    return Ews.Common.EwsValueTypeEnum.Boolean;
                case BuiltInType.SByte:
                case BuiltInType.Byte:
                case BuiltInType.Int16:
                case BuiltInType.UInt16:
                case BuiltInType.Int32:
                    return Ews.Common.EwsValueTypeEnum.Integer;
                case BuiltInType.UInt32:
                case BuiltInType.Int64:
                case BuiltInType.UInt64:
                    return Ews.Common.EwsValueTypeEnum.Long;
                case BuiltInType.Float:
                case BuiltInType.Double:
                    return Ews.Common.EwsValueTypeEnum.Double;
                case BuiltInType.String:
                case BuiltInType.NodeId:
                case BuiltInType.QualifiedName:
                case BuiltInType.LocalizedText:
                case BuiltInType.Guid:
                    return Ews.Common.EwsValueTypeEnum.String;
                case BuiltInType.DateTime:
                    return Ews.Common.EwsValueTypeEnum.DateTime;
                case BuiltInType.ByteString:
                    return Ews.Common.EwsValueTypeEnum.String; // Base64 encoded
                default:
                    return Ews.Common.EwsValueTypeEnum.String;
            }
        }

        /// <summary>
        /// Gets the EWS value state based on OPC UA status code
        /// </summary>
        /// <param name="statusCode">The OPC UA status code</param>
        /// <returns>The corresponding EWS value state</returns>
        public static Ews.Common.EwsValueStateEnum GetEwsValueState(StatusCode statusCode)
        {
            if (StatusCode.IsGood(statusCode))
            {
                return Ews.Common.EwsValueStateEnum.Good;
            }
            else if (StatusCode.IsUncertain(statusCode))
            {
                return Ews.Common.EwsValueStateEnum.Uncertain;
            }
            else
            {
                return Ews.Common.EwsValueStateEnum.Bad;
            }
        }

        /// <summary>
        /// Validates if a string is a valid OPC UA NodeId
        /// </summary>
        /// <param name="nodeIdString">The node ID string to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidNodeId(string nodeIdString)
        {
            if (string.IsNullOrEmpty(nodeIdString)) return false;
            
            try
            {
                NodeId.Parse(nodeIdString);
                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Creates a formatted node mapping container ID
        /// </summary>
        /// <param name="index">The mapping index</param>
        /// <returns>The formatted container ID</returns>
        public static string GetNodeMappingContainerId(int index)
        {
            return string.Format(NodeMappingTemplateId, index);
        }

        /// <summary>
        /// Creates a formatted node mapping node ID field ID
        /// </summary>
        /// <param name="index">The mapping index</param>
        /// <returns>The formatted field ID</returns>
        public static string GetNodeMappingNodeIdId(int index)
        {
            return string.Format(NodeMappingNodeIdId, index);
        }

        /// <summary>
        /// Creates a formatted node mapping value field ID
        /// </summary>
        /// <param name="index">The mapping index</param>
        /// <returns>The formatted field ID</returns>
        public static string GetNodeMappingValueId(int index)
        {
            return string.Format(NodeMappingValueId, index);
        }

        /// <summary>
        /// Creates a formatted node mapping quality field ID
        /// </summary>
        /// <param name="index">The mapping index</param>
        /// <returns>The formatted field ID</returns>
        public static string GetNodeMappingQualityId(int index)
        {
            return string.Format(NodeMappingQualityId, index);
        }

        /// <summary>
        /// Creates a formatted node mapping timestamp field ID
        /// </summary>
        /// <param name="index">The mapping index</param>
        /// <returns>The formatted field ID</returns>
        public static string GetNodeMappingTimestampId(int index)
        {
            return string.Format(NodeMappingTimestampId, index);
        }

        /// <summary>
        /// Creates a formatted node mapping history field ID
        /// </summary>
        /// <param name="index">The mapping index</param>
        /// <returns>The formatted field ID</returns>
        public static string GetNodeMappingHistoryId(int index)
        {
            return string.Format(NodeMappingHistoryId, index);
        }

        #endregion
    }
}
