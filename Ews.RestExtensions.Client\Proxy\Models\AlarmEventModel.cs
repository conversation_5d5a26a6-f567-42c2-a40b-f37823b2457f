﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// An AlarmEvent represents the transition of an Alarm through the state
    /// transition model implemented.
    /// Every transition for an Alarm, including a return to normal generates
    /// an AlarmEvent instance.
    /// </summary>
    public partial class AlarmEventModel
    {
        /// <summary>
        /// Initializes a new instance of the AlarmEventModel class.
        /// </summary>
        public AlarmEventModel() { }

        /// <summary>
        /// Initializes a new instance of the AlarmEventModel class.
        /// </summary>
        public AlarmEventModel(string alarmId = default(string), string acknowledgeable = default(string), DateTime? occurredOn = default(DateTime?), DateTime? lastTransitionedOn = default(DateTime?), string message = default(string), int? priority = default(int?), string state = default(string), string type = default(string), string id = default(string))
        {
            AlarmId = alarmId;
            Acknowledgeable = acknowledgeable;
            OccurredOn = occurredOn;
            LastTransitionedOn = lastTransitionedOn;
            Message = message;
            Priority = priority;
            State = state;
            Type = type;
            Id = id;
        }

        /// <summary>
        /// Optional Id of the associated Alarm.
        /// </summary>
        [JsonProperty(PropertyName = "AlarmId")]
        public string AlarmId { get; set; }

        /// <summary>
        /// Acknowledge restrictions of the AlarmEvent. Possible values
        /// include: 'No', 'Yes', 'Required'
        /// </summary>
        [JsonProperty(PropertyName = "Acknowledgeable")]
        public string Acknowledgeable { get; set; }

        /// <summary>
        /// DateTime in UTC when the Alarm most recently transitioned into and
        /// �alarmed� state - that is not in the "Normal" state.
        /// </summary>
        [JsonProperty(PropertyName = "OccurredOn")]
        public DateTime? OccurredOn { get; set; }

        /// <summary>
        /// DateTime oin UTC when the transition captured by this AlarmEvent
        /// occurred.
        /// </summary>
        [JsonProperty(PropertyName = "LastTransitionedOn")]
        public DateTime? LastTransitionedOn { get; set; }

        /// <summary>
        /// Human readable message for the AlarmEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Message")]
        public string Message { get; set; }

        /// <summary>
        /// Priority value of the AlarmEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Priority")]
        public int? Priority { get; set; }

        /// <summary>
        /// State this AlarmEvent transition is documenting. Possible values
        /// include: 'Normal', 'Active', 'Acknowledged', 'Reset', 'Disabled'
        /// </summary>
        [JsonProperty(PropertyName = "State")]
        public string State { get; set; }

        /// <summary>
        /// Server specific type of the AlarmEvent.
        /// </summary>
        [JsonProperty(PropertyName = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
