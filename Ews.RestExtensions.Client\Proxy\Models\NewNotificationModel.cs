﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// Describes how a Notification will be created for a give Subscription.
    /// </summary>
    public partial class NewNotificationModel
    {
        /// <summary>
        /// Initializes a new instance of the NewNotificationModel class.
        /// </summary>
        public NewNotificationModel() { }

        /// <summary>
        /// Initializes a new instance of the NewNotificationModel class.
        /// </summary>
        public NewNotificationModel(string subscriptionId = default(string), bool? changesOnly = default(bool?))
        {
            SubscriptionId = subscriptionId;
            ChangesOnly = changesOnly;
        }

        /// <summary>
        /// Id of the Subscription the Notification will be created under.
        /// </summary>
        [JsonProperty(PropertyName = "SubscriptionId")]
        public string SubscriptionId { get; set; }

        /// <summary>
        /// If true, all items will be returned.  If false, only changes since
        /// the last Notification for the Subscription will be created.
        /// </summary>
        [JsonProperty(PropertyName = "ChangesOnly")]
        public bool? ChangesOnly { get; set; }

    }
}
