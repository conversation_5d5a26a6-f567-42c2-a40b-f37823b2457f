﻿// Code generated by Microsoft (R) AutoRest Code Generator ********
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Threading;
    using System.Threading.Tasks;
    using Microsoft.Rest;
    using Models;

    /// <summary>
    /// Extension methods for Subscriptions.
    /// </summary>
    public static partial class SubscriptionsExtensions
    {
            /// <summary>
            /// Creates a new Subscription and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Subscription will be created.
            /// </param>
            public static SubscriptionModel Create(this ISubscriptions operations, NewSubscriptionModel newItem)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).CreateAsync(newItem), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Creates a new Subscription and returns it.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='newItem'>
            /// Definition of how the Subscription will be created.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<SubscriptionModel> CreateAsync(this ISubscriptions operations, NewSubscriptionModel newItem, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.CreateWithHttpMessagesAsync(newItem, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns all Subscription which fit the filter criteria supplied. All
            /// filter parameters are optional. Subscription will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='type'>
            /// Return only Subscription with this Type value. Possible values include:
            /// 'ValueItemChanged', 'AlarmEventChanged', 'SystemEventChanged',
            /// 'HierarchyChanged'
            /// </param>
            /// <param name='orderBy'>
            /// Subscription will be returned in this order.  ExpiresAscending by default.
            /// Possible values include: 'ExpiresAscending', 'ExpiresDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Subscription that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Subscription that should be skipped before items are returned.
            /// 0 by default.
            /// </param>
            public static IList<SubscriptionModel> Retrieve(this ISubscriptions operations, string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?))
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).RetrieveAsync(type, orderBy, take, skip), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns all Subscription which fit the filter criteria supplied. All
            /// filter parameters are optional. Subscription will be ordered and paged as
            /// requested.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='type'>
            /// Return only Subscription with this Type value. Possible values include:
            /// 'ValueItemChanged', 'AlarmEventChanged', 'SystemEventChanged',
            /// 'HierarchyChanged'
            /// </param>
            /// <param name='orderBy'>
            /// Subscription will be returned in this order.  ExpiresAscending by default.
            /// Possible values include: 'ExpiresAscending', 'ExpiresDescending'
            /// </param>
            /// <param name='take'>
            /// Number of Subscription that should be returned.  200 by default.
            /// </param>
            /// <param name='skip'>
            /// Number of Subscription that should be skipped before items are returned.
            /// 0 by default.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<IList<SubscriptionModel>> RetrieveAsync(this ISubscriptions operations, string type = default(string), string orderBy = default(string), int? take = default(int?), int? skip = default(int?), CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveWithHttpMessagesAsync(type, orderBy, take, skip, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription requested.  Must be double URL encoded.
            /// </param>
            public static SubscriptionModel RetrieveById(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).RetrieveByIdAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription requested.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<SubscriptionModel> RetrieveByIdAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveByIdWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the ExpiresOn property for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription whose ExpiresOn property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            public static DateTime? RetrieveExpiresOn(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).RetrieveExpiresOnAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the ExpiresOn property for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription whose ExpiresOn property is to be returned.  Must
            /// be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<DateTime?> RetrieveExpiresOnAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveExpiresOnWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the Type property for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription whose Type property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static string RetrieveType(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).RetrieveTypeAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the Type property for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription whose Type property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<string> RetrieveTypeAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveTypeWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Returns the IsActive property for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription whose IsActive property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            public static bool? RetrieveIsActive(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).RetrieveIsActiveAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Returns the IsActive property for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription whose IsActive property is to be returned.  Must be
            /// double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<bool?> RetrieveIsActiveAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RetrieveIsActiveWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Renews the Subscription with the Id provided by the requested number of
            /// minutes.
            /// Returns the renewed Subscription.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to be renewed.  Must be double URL encoded.
            /// </param>
            /// <param name='minutes'>
            /// Value in minutes to extend the Subscription.
            /// </param>
            public static SubscriptionModel Renew(this ISubscriptions operations, string id, int minutes)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).RenewAsync(id, minutes), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Renews the Subscription with the Id provided by the requested number of
            /// minutes.
            /// Returns the renewed Subscription.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to be renewed.  Must be double URL encoded.
            /// </param>
            /// <param name='minutes'>
            /// Value in minutes to extend the Subscription.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<SubscriptionModel> RenewAsync(this ISubscriptions operations, string id, int minutes, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.RenewWithHttpMessagesAsync(id, minutes, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Terminates the Subscription with the Id.  Terminated Subscription no
            /// longer can be used to create Notification sessions.
            /// Returns the terminated Subscription.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to update.  Requires double URL encoding.
            /// </param>
            public static SubscriptionModel Terminate(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).TerminateAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Terminates the Subscription with the Id.  Terminated Subscription no
            /// longer can be used to create Notification sessions.
            /// Returns the terminated Subscription.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to update.  Requires double URL encoding.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<SubscriptionModel> TerminateAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.TerminateWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes the Subscription with the Id provided.  All Notification and
            /// NotificationItem created from the Subscription will also be deleted.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to be deleted.  Must be double URL encoded.
            /// </param>
            public static object Delete(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).DeleteAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes the Subscription with the Id provided.  All Notification and
            /// NotificationItem created from the Subscription will also be deleted.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to be deleted.  Must be double URL encoded.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

            /// <summary>
            /// Deletes Notification for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to act on.  Requires double URL encoding.
            /// </param>
            public static object DeleteNotifications(this ISubscriptions operations, string id)
            {
                return Task.Factory.StartNew(s => ((ISubscriptions)s).DeleteNotificationsAsync(id), operations, CancellationToken.None, TaskCreationOptions.None, TaskScheduler.Default).Unwrap().GetAwaiter().GetResult();
            }

            /// <summary>
            /// Deletes Notification for the Subscription with the Id provided.
            /// </summary>
            /// <param name='operations'>
            /// The operations group for this extension method.
            /// </param>
            /// <param name='id'>
            /// Id of the Subscription to act on.  Requires double URL encoding.
            /// </param>
            /// <param name='cancellationToken'>
            /// The cancellation token.
            /// </param>
            public static async Task<object> DeleteNotificationsAsync(this ISubscriptions operations, string id, CancellationToken cancellationToken = default(CancellationToken))
            {
                using (var _result = await operations.DeleteNotificationsWithHttpMessagesAsync(id, null, cancellationToken).ConfigureAwait(false))
                {
                    return _result.Body;
                }
            }

    }
}
