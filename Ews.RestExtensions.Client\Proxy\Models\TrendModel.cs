﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// A Trend references a specific Value for which historical value
    /// "trending" has been enabled.
    /// The existence of a Trend does not imply that history exists; only that
    /// history will be captured for a Value.
    /// </summary>
    public partial class TrendModel
    {
        /// <summary>
        /// Initializes a new instance of the TrendModel class.
        /// </summary>
        public TrendModel() { }

        /// <summary>
        /// Initializes a new instance of the TrendModel class.
        /// </summary>
        public TrendModel(string type = default(string), string unit = default(string), string valueId = default(string), string name = default(string), string description = default(string), string parentId = default(string), string id = default(string))
        {
            Type = type;
            Unit = unit;
            ValueId = valueId;
            Name = name;
            Description = description;
            ParentId = parentId;
            Id = id;
        }

        /// <summary>
        /// Type of the associated Value. Possible values include: 'DateTime',
        /// 'Boolean', 'String', 'Double', 'Long', 'Integer', 'Duration'
        /// </summary>
        [JsonProperty(PropertyName = "Type")]
        public string Type { get; set; }

        /// <summary>
        /// Unit of the associated Value.
        /// </summary>
        [JsonProperty(PropertyName = "Unit")]
        public string Unit { get; set; }

        /// <summary>
        /// Id of the Value for which trending data will be captured.
        /// </summary>
        [JsonProperty(PropertyName = "ValueId")]
        public string ValueId { get; set; }

        /// <summary>
        /// Name of the Trend.
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Optional description of the Trend.
        /// </summary>
        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        /// <summary>
        /// Id of the Container which the Trend is found in.
        /// </summary>
        [JsonProperty(PropertyName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
