﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="14.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{AB219EE4-9A51-4C99-967C-2B85970C68AF}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Ews.RestExtensions.Client</RootNamespace>
    <AssemblyName>Ews.RestExtensions.Client</AssemblyName>
    <TargetFrameworkVersion>v4.5.2</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Microsoft.Rest.ClientRuntime, Version=2.0.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Rest.ClientRuntime.2.3.10\lib\net452\Microsoft.Rest.ClientRuntime.dll</HintPath>
    </Reference>
    <Reference Include="Newtonsoft.Json, Version=10.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.10.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http.Formatting, Version=5.2.3.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.AspNet.WebApi.Client.5.2.3\lib\net45\System.Net.Http.Formatting.dll</HintPath>
    </Reference>
    <Reference Include="System.Net.Http.WebRequest" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.Web" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="BearerToken.cs" />
    <Compile Include="EwsRestGateway.cs" />
    <Compile Include="ExtensionMethods.cs" />
    <Compile Include="Proxy\AlarmEvents.cs" />
    <Compile Include="Proxy\AlarmEventsExtensions.cs" />
    <Compile Include="Proxy\AlarmEventTypes.cs" />
    <Compile Include="Proxy\AlarmEventTypesExtensions.cs" />
    <Compile Include="Proxy\Alarms.cs" />
    <Compile Include="Proxy\AlarmsExtensions.cs" />
    <Compile Include="Proxy\Containers.cs" />
    <Compile Include="Proxy\ContainersExtensions.cs" />
    <Compile Include="Proxy\IAlarmEvents.cs" />
    <Compile Include="Proxy\IAlarmEventTypes.cs" />
    <Compile Include="Proxy\IAlarms.cs" />
    <Compile Include="Proxy\IContainers.cs" />
    <Compile Include="Proxy\IEwsRestGateway.cs" />
    <Compile Include="Proxy\INotifications.cs" />
    <Compile Include="Proxy\IRoot.cs" />
    <Compile Include="Proxy\ISubscriptions.cs" />
    <Compile Include="Proxy\ISystemEvents.cs" />
    <Compile Include="Proxy\ISystemEventTypes.cs" />
    <Compile Include="Proxy\ITrends.cs" />
    <Compile Include="Proxy\ITrendSamples.cs" />
    <Compile Include="Proxy\IValues.cs" />
    <Compile Include="Proxy\Models\AlarmEventInfo.cs" />
    <Compile Include="Proxy\Models\AlarmEventModel.cs" />
    <Compile Include="Proxy\Models\AlarmEventStateChangeInfo.cs" />
    <Compile Include="Proxy\Models\AlarmEventTypeModel.cs" />
    <Compile Include="Proxy\Models\AlarmModel.cs" />
    <Compile Include="Proxy\Models\ContainerModel.cs" />
    <Compile Include="Proxy\Models\DateRange.cs" />
    <Compile Include="Proxy\Models\IItem.cs" />
    <Compile Include="Proxy\Models\NewAlarmEventTypeModel.cs" />
    <Compile Include="Proxy\Models\NewAlarmModel.cs" />
    <Compile Include="Proxy\Models\NewContainerModel.cs" />
    <Compile Include="Proxy\Models\NewNotificationModel.cs" />
    <Compile Include="Proxy\Models\NewSubscriptionModel.cs" />
    <Compile Include="Proxy\Models\NewTrendModel.cs" />
    <Compile Include="Proxy\Models\NewValueModel.cs" />
    <Compile Include="Proxy\Models\NotificationItemModel.cs" />
    <Compile Include="Proxy\Models\NotificationModel.cs" />
    <Compile Include="Proxy\Models\SubscriptionModel.cs" />
    <Compile Include="Proxy\Models\SystemEventModel.cs" />
    <Compile Include="Proxy\Models\SystemEventTypeModel.cs" />
    <Compile Include="Proxy\Models\TrendModel.cs" />
    <Compile Include="Proxy\Models\TrendSampleModel.cs" />
    <Compile Include="Proxy\Models\UpdateAlarmEventTypeModel.cs" />
    <Compile Include="Proxy\Models\UpdateAlarmModel.cs" />
    <Compile Include="Proxy\Models\UpdateContainerModel.cs" />
    <Compile Include="Proxy\Models\UpdateTrendModel.cs" />
    <Compile Include="Proxy\Models\UpdateValueModel.cs" />
    <Compile Include="Proxy\Models\ValueModel.cs" />
    <Compile Include="Proxy\EwsRestGateway.cs" />
    <Compile Include="Proxy\Notifications.cs" />
    <Compile Include="Proxy\NotificationsExtensions.cs" />
    <Compile Include="Proxy\Root.cs" />
    <Compile Include="Proxy\RootExtensions.cs" />
    <Compile Include="Proxy\Subscriptions.cs" />
    <Compile Include="Proxy\SubscriptionsExtensions.cs" />
    <Compile Include="Proxy\SystemEvents.cs" />
    <Compile Include="Proxy\SystemEventsExtensions.cs" />
    <Compile Include="Proxy\SystemEventTypes.cs" />
    <Compile Include="Proxy\SystemEventTypesExtensions.cs" />
    <Compile Include="Proxy\Trends.cs" />
    <Compile Include="Proxy\TrendSamples.cs" />
    <Compile Include="Proxy\TrendSamplesExtensions.cs" />
    <Compile Include="Proxy\TrendsExtensions.cs" />
    <Compile Include="Proxy\Values.cs" />
    <Compile Include="Proxy\ValuesExtensions.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
  </ItemGroup>
  <ItemGroup>
    <None Include="app.config" />
    <None Include="packages.config" />
    <None Include="Swagger Metadata.json" />
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>