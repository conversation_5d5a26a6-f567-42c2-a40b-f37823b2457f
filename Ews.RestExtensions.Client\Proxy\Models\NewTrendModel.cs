﻿// Code generated by Microsoft (R) AutoRest Code Generator 0.16.0.0
// Changes may cause incorrect behavior and will be lost if the code is
// regenerated.

namespace Ews.RestExtensions.Client.Proxy.Models
{
    using System;
    using System.Linq;
    using System.Collections.Generic;
    using Newtonsoft.Json;
    using Microsoft.Rest;
    using Microsoft.Rest.Serialization;

    /// <summary>
    /// Describes how an Container will be created.
    /// </summary>
    public partial class NewTrendModel
    {
        /// <summary>
        /// Initializes a new instance of the NewTrendModel class.
        /// </summary>
        public NewTrendModel() { }

        /// <summary>
        /// Initializes a new instance of the NewTrendModel class.
        /// </summary>
        public NewTrendModel(string valueId = default(string), string name = default(string), string description = default(string), string parentId = default(string), string id = default(string))
        {
            ValueId = valueId;
            Name = name;
            Description = description;
            ParentId = parentId;
            Id = id;
        }

        /// <summary>
        /// Id of the Value for which trending data will be captured.
        /// </summary>
        [JsonProperty(PropertyName = "ValueId")]
        public string ValueId { get; set; }

        /// <summary>
        /// Name of the Trend.
        /// </summary>
        [JsonProperty(PropertyName = "Name")]
        public string Name { get; set; }

        /// <summary>
        /// Optional description of the Trend.
        /// </summary>
        [JsonProperty(PropertyName = "Description")]
        public string Description { get; set; }

        /// <summary>
        /// Id of the Container which the Trend is found in.
        /// </summary>
        [JsonProperty(PropertyName = "ParentId")]
        public string ParentId { get; set; }

        /// <summary>
        /// Case sensitive identifier for the entity.
        /// </summary>
        [JsonProperty(PropertyName = "Id")]
        public string Id { get; set; }

    }
}
